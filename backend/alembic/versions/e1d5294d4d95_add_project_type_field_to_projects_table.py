"""Add project_type field to projects table

Revision ID: e1d5294d4d95
Revises: ec6f97bedc46
Create Date: 2025-07-30 01:25:23.841113

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e1d5294d4d95'
down_revision: Union[str, None] = 'ec6f97bedc46'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Create the enum type first
    project_type_enum = sa.Enum('GENERATE_FROM_REQUIREMENTS', 'IMPORT_TEST_CASES', name='projecttype')
    project_type_enum.create(op.get_bind())

    # Add the column as nullable first
    op.add_column('projects', sa.Column('project_type', project_type_enum, nullable=True))

    # Set default value for existing projects (Type 1 - Generate from requirements)
    op.execute("UPDATE projects SET project_type = 'GENERATE_FROM_REQUIREMENTS' WHERE project_type IS NULL")

    # Now make the column non-nullable
    op.alter_column('projects', 'project_type', nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('projects', 'project_type')
    # Drop the enum type
    sa.Enum(name='projecttype').drop(op.get_bind())
    # ### end Alembic commands ###
