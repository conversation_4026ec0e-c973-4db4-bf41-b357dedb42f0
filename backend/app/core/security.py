from datetime import datetime, timedelta, timezone
from typing import Any, <PERSON>, Optional, Tuple
from jose import jwt, JW<PERSON>rror
from passlib.context import CryptContext
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_access_token(
    subject: Union[str, Any], expires_delta: timedelta = None
) -> str:
    now = datetime.now(timezone.utc)
    if expires_delta:
        expire = now + expires_delta
    else:
        expire = now + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

    # Convert to timestamp for JWT
    expire_timestamp = expire.timestamp()
    current_timestamp = now.timestamp()

    to_encode = {"exp": expire_timestamp, "sub": str(subject), "iat": current_timestamp}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)

    logger.debug(f"Created token for user {subject}, expires at {expire} UTC (timestamp: {expire_timestamp})")

    return encoded_jwt

def create_password_reset_token(email: str) -> str:
    """Create a password reset token that expires in 1 hour."""
    now = datetime.now(timezone.utc)
    expire = now + timedelta(hours=1)  # Password reset tokens expire in 1 hour

    expire_timestamp = expire.timestamp()
    current_timestamp = now.timestamp()

    to_encode = {
        "exp": expire_timestamp,
        "sub": email,
        "iat": current_timestamp,
        "type": "password_reset"
    }
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)

    logger.info(f"Created password reset token for {email}, expires at {expire} UTC")

    return encoded_jwt

def verify_password_reset_token(token: str) -> Union[str, None]:
    """Verify password reset token and return email if valid."""
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )

        # Check if it's a password reset token
        if payload.get("type") != "password_reset":
            logger.error("Token is not a password reset token")
            return None

        email = payload.get("sub")
        logger.info(f"Password reset token verification successful for: {email}")
        return email
    except jwt.ExpiredSignatureError:
        logger.error("Password reset token expired")
        return None
    except jwt.JWTError as e:
        logger.error(f"Password reset token verification failed: {e}")
        return None

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def verify_token(token: str) -> Union[str, None]:
    logger.debug("=== TOKEN VERIFICATION STARTED ===")
    logger.debug(f"Token length: {len(token) if token else 0}")
    logger.debug(f"Token prefix: {token[:50]}..." if token and len(token) > 50 else f"Full token: {token}")

    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )

        # Log token verification details
        exp = payload.get("exp")
        sub = payload.get("sub")
        current_time = datetime.now(timezone.utc).timestamp()

        if exp:
            time_until_expiry = exp - current_time
            logger.info(f"Token verification for user {sub}: expires in {time_until_expiry:.2f} seconds")

            if exp < current_time:
                logger.error(f"Token expired for user {sub}: expired {current_time - exp:.2f} seconds ago")
                return None
        return sub
    except jwt.ExpiredSignatureError as e:
        logger.error(f"Token verification failed: signature expired - {e}")
        return None
    except jwt.JWTError as e:
        logger.error(f"Token verification failed: JWT error - {e}")
        return None
    except Exception as e:
        logger.error(f"Token verification failed: unexpected error - {e}")
        return None


def create_tokens_for_user(user_id: int) -> Tuple[str, str]:
    """
    Create both access and refresh tokens for a user.

    Returns:
        Tuple of (access_token, refresh_token_string)
    """
    # Create access token with shorter expiration
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=user_id, expires_delta=access_token_expires
    )

    # Import here to avoid circular imports
    from app.crud.refresh_token import create_refresh_token
    from app.db.session import SessionLocal

    # Create refresh token in database
    db = SessionLocal()
    try:
        refresh_token_obj = create_refresh_token(
            db=db,
            user_id=user_id,
            expires_in_hours=2  # Refresh tokens last 2 hours (allows for 1 hour inactivity + buffer)
        )
        refresh_token_string = refresh_token_obj.token
    finally:
        db.close()

    logger.info(f"Created token pair for user {user_id}")
    return access_token, refresh_token_string


def refresh_access_token(refresh_token: str) -> Optional[Tuple[str, str]]:
    """
    Create a new access token using a refresh token.

    Args:
        refresh_token: The refresh token string

    Returns:
        Tuple of (new_access_token, new_refresh_token) or None if invalid
    """
    from app.crud.refresh_token import validate_and_refresh_token, extend_token_expiration
    from app.db.session import SessionLocal

    db = SessionLocal()
    try:
        # Validate the refresh token
        token_obj = validate_and_refresh_token(db, refresh_token)
        if not token_obj:
            logger.warning("Invalid refresh token provided")
            return None

        # Create new access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        new_access_token = create_access_token(
            subject=token_obj.user_id, expires_delta=access_token_expires
        )

        # Update last used timestamp but don't extend expiration
        # This ensures proper 1-hour inactivity timeout
        from app.crud.refresh_token import update_token_last_used
        update_token_last_used(db, refresh_token)

        logger.info(f"Refreshed access token for user {token_obj.user_id}")
        return new_access_token, refresh_token

    except Exception as e:
        logger.error(f"Error refreshing access token: {e}")
        return None
    finally:
        db.close()


def revoke_refresh_token(refresh_token: str, reason: str = "logout") -> bool:
    """
    Revoke a refresh token.

    Args:
        refresh_token: The refresh token string to revoke
        reason: Reason for revocation

    Returns:
        True if successfully revoked, False otherwise
    """
    from app.crud.refresh_token import revoke_refresh_token as crud_revoke
    from app.db.session import SessionLocal

    db = SessionLocal()
    try:
        return crud_revoke(db, refresh_token, reason)
    finally:
        db.close()


def revoke_all_user_tokens(user_id: int, reason: str = "logout_all") -> int:
    """
    Revoke all refresh tokens for a user.

    Args:
        user_id: The user ID
        reason: Reason for revocation

    Returns:
        Number of tokens revoked
    """
    from app.crud.refresh_token import revoke_user_refresh_tokens
    from app.db.session import SessionLocal

    db = SessionLocal()
    try:
        return revoke_user_refresh_tokens(db, user_id, reason)
    finally:
        db.close()
