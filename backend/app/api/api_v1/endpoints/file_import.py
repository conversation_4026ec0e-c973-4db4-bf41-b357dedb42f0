from typing import List, Di<PERSON>, Tu<PERSON>, Set
import re
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, status
from sqlalchemy.orm import Session
from app.api.deps import get_db, get_current_active_user
from app.crud.project import get_project, is_project_member
from app.crud.requirement import create_requirement as crud_create_requirement, get_requirements_by_project_id, update_requirement
from app.crud.test_case import create_test_case, get_test_cases_for_requirement, delete_test_case, create_test_cases_for_requirement_bulk
from app.models.user import User
from app.models.project import ProjectType
from app.schemas.project import (
    FileUploadResponse, SheetPreview, ImportRequest, ImportResponse,
    ColumnMapping, RequirementChangeSummary
)
from app.schemas.requirement import RequirementCreate, RequirementUpdate, TestCaseCreate, TestCase
from app.services.file_import_service import file_import_service
from app.services.requirement_generation_service import requirement_generation_service
from app.utils.slug import generate_requirement_slug
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

# Test case comparison utility functions
def normalize_text_for_comparison(text: str) -> str:
    """Normalize text for comparison by removing extra whitespace and converting to lowercase."""
    if not text:
        return ""
    # Remove extra whitespace, newlines, and convert to lowercase
    normalized = re.sub(r'\s+', ' ', text.strip().lower())
    return normalized

def are_test_cases_similar(existing_test_case: TestCase, imported_test_case, similarity_threshold: float = 0.9) -> bool:
    """
    Compare two test cases to determine if they are the same.
    Primary comparison is based on steps content.
    """
    existing_steps = normalize_text_for_comparison(existing_test_case.steps or "")
    imported_steps = normalize_text_for_comparison(imported_test_case.steps or "")

    # If steps are identical, consider them the same
    if existing_steps == imported_steps:
        return True

    # If steps are very similar (simple similarity check)
    if existing_steps and imported_steps:
        # Calculate simple similarity based on common words
        existing_words = set(existing_steps.split())
        imported_words = set(imported_steps.split())

        if len(existing_words) == 0 and len(imported_words) == 0:
            return True

        intersection = existing_words.intersection(imported_words)
        union = existing_words.union(imported_words)

        if len(union) > 0:
            similarity = len(intersection) / len(union)
            return similarity >= similarity_threshold

    return False

def find_matching_test_case(imported_test_case, existing_test_cases: List[TestCase]) -> TestCase:
    """Find a matching test case from existing test cases."""
    for existing_test_case in existing_test_cases:
        if are_test_cases_similar(existing_test_case, imported_test_case):
            return existing_test_case
    return None

def get_next_test_case_number(existing_test_cases: List[TestCase]) -> int:
    """Get the next sequential test case number based on existing test case titles."""
    max_number = 0

    for test_case in existing_test_cases:
        # Extract number from titles like "Test Case 01", "Test Case 02", etc.
        match = re.search(r'Test Case (\d+)', test_case.title, re.IGNORECASE)
        if match:
            number = int(match.group(1))
            max_number = max(max_number, number)

    return max_number + 1

def synchronize_test_cases(db: Session, requirement_id: int, existing_test_cases: List[TestCase], imported_test_cases: List) -> Dict:
    """
    Synchronize test cases for a requirement by comparing existing and imported test cases.
    Returns a summary of changes made.
    """
    # Track changes
    new_test_cases_added = 0
    test_cases_deleted = 0
    test_cases_updated = 0

    # Create sets to track which test cases have been matched
    matched_existing_ids = set()
    matched_imported_indices = set()

    # First pass: Find matches between existing and imported test cases
    for i, imported_test_case in enumerate(imported_test_cases):
        matching_existing = find_matching_test_case(imported_test_case, existing_test_cases)
        if matching_existing:
            matched_existing_ids.add(matching_existing.id)
            matched_imported_indices.add(i)
            # Note: We could update the existing test case here if needed
            # For now, we assume matching test cases don't need updates

    # Second pass: Delete test cases that are no longer present
    for existing_test_case in existing_test_cases:
        if existing_test_case.id not in matched_existing_ids:
            delete_test_case(db, existing_test_case.id)
            test_cases_deleted += 1

    # Third pass: Add new test cases that don't match any existing ones in bulk
    next_test_case_number = get_next_test_case_number(existing_test_cases)
    new_test_cases_to_create = []

    for i, imported_test_case in enumerate(imported_test_cases):
        if i not in matched_imported_indices:
            # This is a new test case
            test_case_data = TestCaseCreate(
                title=imported_test_case.title or f"Test Case {next_test_case_number:02d}",
                steps=imported_test_case.steps,
                expected_result=imported_test_case.expected_result or "",
                notes=imported_test_case.notes,
                custom_id=None  # Will be auto-generated
            )

            new_test_cases_to_create.append(test_case_data)
            next_test_case_number += 1

    # Create all new test cases in bulk for better performance
    if new_test_cases_to_create:
        create_test_cases_for_requirement_bulk(db, requirement_id, new_test_cases_to_create)
        new_test_cases_added = len(new_test_cases_to_create)

    return {
        "new_test_cases_added": new_test_cases_added,
        "test_cases_deleted": test_cases_deleted,
        "test_cases_updated": test_cases_updated
    }

@router.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """Upload Excel file for import."""
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")

    # Additional file size validation (10MB limit)
    if file.size and file.size > 10 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="File size exceeds 10MB limit")

    return await file_import_service.upload_file(file)

@router.get("/preview/{file_id}/{sheet_name}", response_model=SheetPreview)
def get_sheet_preview(
    file_id: str,
    sheet_name: str,
    preview_rows: int = 5,
    starting_row: int = 1,
    current_user: User = Depends(get_current_active_user)
):
    """Get preview of a specific sheet."""
    return file_import_service.get_sheet_preview(file_id, sheet_name, preview_rows, starting_row)

@router.post("/import/{project_id}", response_model=ImportResponse)
async def import_test_cases(
    project_id: int,
    import_request: ImportRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Import test cases from uploaded file and generate requirements."""
    
    # Check if user has access to the project
    project = get_project(db, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    if not is_project_member(db, project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to import test cases to this project"
        )
    
    # Verify project is Type 2 (import test cases)
    if project.project_type != ProjectType.IMPORT_TEST_CASES:
        raise HTTPException(
            status_code=400,
            detail="This project is not configured for test case import"
        )
    
    try:
        # Get existing requirements for intelligent import
        existing_requirements = get_requirements_by_project_id(db, project_id)
        existing_req_names = [req.name for req in existing_requirements]

        # Parse the file and extract test cases
        imported_requirements = file_import_service.parse_and_import(
            import_request.file_id,
            import_request.selected_sheets,
            import_request.column_mapping,
            existing_req_names
        )
        
        if not imported_requirements:
            raise HTTPException(status_code=400, detail="No valid test cases found in the file")
        
        # Separate new and existing requirements for bulk processing
        new_requirements = []
        existing_requirements_to_update = []
        created_requirements = []
        total_test_cases = 0
        change_summaries = []

        # First pass: generate titles and separate new vs existing requirements
        for imported_req in imported_requirements:
            # Generate simple incremental titles for test cases that don't have them
            for i, test_case in enumerate(imported_req.test_cases, 1):
                if not test_case.title:
                    test_case.title = f"Test Case {i:02d}"

            # Check if requirement already exists
            existing_requirement = None
            for existing_req in existing_requirements:
                if existing_req.name == imported_req.name:
                    existing_requirement = existing_req
                    break

            if existing_requirement:
                existing_requirements_to_update.append((existing_requirement, imported_req))
            else:
                new_requirements.append(imported_req)

        # Process existing requirements (keep individual processing for complexity)
        for existing_requirement, imported_req in existing_requirements_to_update:
            # Update existing requirement - get existing test cases and combine with new ones
            existing_test_cases = get_test_cases_for_requirement(db, existing_requirement.id)

            # Combine existing and new test cases for description generation
            all_test_cases = imported_req.test_cases.copy()

            # Generate updated requirement description from all test cases
            description = await requirement_generation_service.generate_requirement_description(
                imported_req.name, all_test_cases
            )

            # Update requirement description
            requirement_update = RequirementUpdate(description=description)
            update_requirement(db, existing_requirement.id, requirement_update)

            # Intelligent test case synchronization
            change_summary = synchronize_test_cases(
                db, existing_requirement.id, existing_test_cases, imported_req.test_cases
            )

            total_test_cases += change_summary["new_test_cases_added"]

            # Add change summary for this requirement
            # Add change summary for this requirement
            change_summaries.append(RequirementChangeSummary(
                requirement_name=existing_requirement.name,
                new_test_cases_added=change_summary["new_test_cases_added"],
                test_cases_deleted=change_summary["test_cases_deleted"],
                test_cases_updated=change_summary["test_cases_updated"],
                is_new_requirement=False
            ))

            # Update the imported requirement with the generated description
            imported_req.description = description
            # Note: Don't append to created_requirements here - this is for existing requirements

        # Process new requirements individually to ensure proper mapping
        for imported_req in new_requirements:
            # Generate requirement description
            description = await requirement_generation_service.generate_requirement_description(
                imported_req.name, imported_req.test_cases
            )

            # Create requirement in database
            requirement_data = RequirementCreate(
                name=imported_req.name,
                description=description,
                project_id=project_id,
                tag_names=[]
            )

            db_requirement = crud_create_requirement(db, requirement_data, current_user.id)

            # Create test cases for this requirement in bulk for better performance
            test_cases_to_create = []
            for i, test_case in enumerate(imported_req.test_cases, 1):
                test_case_data = TestCaseCreate(
                    title=test_case.title or f"Test Case {i:02d}",
                    steps=test_case.steps,
                    expected_result=test_case.expected_result or "",
                    notes=test_case.notes,
                    custom_id=None  # Will be auto-generated
                )
                test_cases_to_create.append(test_case_data)

            # Create all test cases in bulk
            if test_cases_to_create:
                create_test_cases_for_requirement_bulk(db, db_requirement.id, test_cases_to_create)
                total_test_cases += len(test_cases_to_create)

            # Add change summary for this new requirement
            change_summaries.append(RequirementChangeSummary(
                requirement_name=imported_req.name,
                new_test_cases_added=len(imported_req.test_cases),
                test_cases_deleted=0,
                test_cases_updated=0,
                is_new_requirement=True
            ))

            # Update the imported requirement with the generated description
            imported_req.description = description
            created_requirements.append(imported_req)
        
        # Clean up the uploaded file
        file_import_service.cleanup_file(import_request.file_id)
        
        # Create summary message with change details
        total_new_requirements = sum(1 for cs in change_summaries if cs.is_new_requirement)
        total_updated_requirements = sum(1 for cs in change_summaries if not cs.is_new_requirement)
        total_new_test_cases = sum(cs.new_test_cases_added for cs in change_summaries)
        total_deleted_test_cases = sum(cs.test_cases_deleted for cs in change_summaries)

        message_parts = []
        if total_new_requirements > 0:
            message_parts.append(f"{total_new_requirements} new requirements created")
        if total_updated_requirements > 0:
            message_parts.append(f"{total_updated_requirements} requirements updated")
        if total_new_test_cases > 0:
            message_parts.append(f"{total_new_test_cases} test cases added")
        if total_deleted_test_cases > 0:
            message_parts.append(f"{total_deleted_test_cases} test cases removed")

        summary_message = "Successfully imported: " + ", ".join(message_parts) if message_parts else "No changes made"

        return ImportResponse(
            requirements=created_requirements,
            total_test_cases=total_test_cases,
            message=summary_message,
            change_summary=change_summaries
        )
        
    except HTTPException:
        raise
    except Exception as e:
        # Clean up the uploaded file on error
        file_import_service.cleanup_file(import_request.file_id)
        raise HTTPException(status_code=500, detail=f"Import failed: {str(e)}")

@router.delete("/cleanup/{file_id}")
def cleanup_file(
    file_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Clean up uploaded file."""
    file_import_service.cleanup_file(file_id)
    return {"message": "File cleaned up successfully"}

@router.get("/validate-mapping/{file_id}")
def validate_column_mapping(
    file_id: str,
    sheet_name: str,
    column_mapping: ColumnMapping,
    current_user: User = Depends(get_current_active_user)
):
    """Validate column mapping against the file structure."""
    try:
        # Get sheet preview to validate columns exist
        preview = file_import_service.get_sheet_preview(file_id, sheet_name, 1)
        headers = preview.headers
        
        errors = []
        
        # Check required columns
        if column_mapping.steps_column not in headers:
            errors.append(f"Steps column '{column_mapping.steps_column}' not found in sheet")
        
        if column_mapping.expected_result_column not in headers:
            errors.append(f"Expected result column '{column_mapping.expected_result_column}' not found in sheet")
        
        # Check optional columns if specified
        if column_mapping.submodule_column and column_mapping.submodule_column not in headers:
            errors.append(f"Submodule column '{column_mapping.submodule_column}' not found in sheet")
        
        if column_mapping.title_column and column_mapping.title_column not in headers:
            errors.append(f"Title column '{column_mapping.title_column}' not found in sheet")
        
        if column_mapping.notes_column and column_mapping.notes_column not in headers:
            errors.append(f"Notes column '{column_mapping.notes_column}' not found in sheet")
        
        # Check starting row
        if column_mapping.starting_row < 1 or column_mapping.starting_row > preview.total_rows:
            errors.append(f"Starting row {column_mapping.starting_row} is out of range (1-{preview.total_rows})")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "available_columns": headers
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Validation failed: {str(e)}")
