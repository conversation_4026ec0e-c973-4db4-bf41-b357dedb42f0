from fastapi import APIRouter

from app.api.api_v1.endpoints import auth, projects, requirements, crawler, code_generation, users, file_import

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(projects.router, prefix="/projects", tags=["projects"])
api_router.include_router(requirements.router, prefix="/requirements", tags=["requirements"])
api_router.include_router(crawler.router, prefix="/crawler", tags=["crawler"])
api_router.include_router(code_generation.router, prefix="/code-generation", tags=["code-generation"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(file_import.router, prefix="/file-import", tags=["file-import"])
