import os
import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from app.models.project import AutomationFramework, ProgrammingLanguage
from app.services.git_service import git_service

logger = logging.getLogger(__name__)

class RepositoryValidationService:
    """Service for validating repository compatibility with selected framework and language."""
    
    def __init__(self):
        # Framework indicators - files/patterns that indicate specific frameworks
        self.framework_indicators = {
            AutomationFramework.SELENIUM: {
                'files': ['requirements.txt', 'pom.xml', 'package.json', 'packages.config'],
                'patterns': [
                    r'selenium[^a-zA-Z]',
                    r'webdriver',
                    r'from selenium',
                    r'import selenium',
                    r'org\.openqa\.selenium',
                    r'selenium-webdriver',
                    r'Selenium\.WebDriver'
                ],
                'directories': ['selenium', 'webdriver']
            },
            AutomationFramework.PLAYWRIGHT: {
                'files': ['requirements.txt', 'pom.xml', 'package.json', 'packages.config'],
                'patterns': [
                    r'playwright[^a-zA-Z]',
                    r'from playwright',
                    r'import playwright',
                    r'com\.microsoft\.playwright',
                    r'@playwright/test',
                    r'Microsoft\.Playwright'
                ],
                'directories': ['playwright']
            }
        }
        
        # Language indicators - file extensions and patterns
        self.language_indicators = {
            ProgrammingLanguage.PYTHON: {
                'extensions': ['.py'],
                'files': ['requirements.txt', 'setup.py', 'pyproject.toml', 'Pipfile'],
                'patterns': [r'python', r'pip', r'pytest', r'unittest']
            },
            ProgrammingLanguage.JAVASCRIPT: {
                'extensions': ['.js', '.ts'],
                'files': ['package.json', 'package-lock.json', 'yarn.lock'],
                'patterns': [r'node', r'npm', r'jest', r'mocha', r'jasmine']
            },
            ProgrammingLanguage.JAVA: {
                'extensions': ['.java'],
                'files': ['pom.xml', 'build.gradle', 'gradle.properties'],
                'patterns': [r'java', r'maven', r'gradle', r'junit', r'testng']
            },
            ProgrammingLanguage.CSHARP: {
                'extensions': ['.cs'],
                'files': ['*.csproj', '*.sln', 'packages.config'],
                'patterns': [r'\.net', r'csharp', r'nunit', r'mstest', r'xunit']
            }
        }

    async def validate_repository_compatibility(
        self,
        project_name: str,
        expected_framework: AutomationFramework,
        expected_language: ProgrammingLanguage
    ) -> Dict[str, Any]:
        """
        Validate if the repository matches the expected framework and language.
        
        Args:
            project_name: Name of the project
            expected_framework: Expected automation framework
            expected_language: Expected programming language
            
        Returns:
            Dictionary with validation results
        """
        try:
            logger.info(f"🔍 Validating repository compatibility for {project_name}")
            
            # Get repository files
            repo_files_data = git_service.get_repository_files(project_name)

            if not repo_files_data:
                return {
                    "is_compatible": True,  # Empty repo is compatible
                    "is_empty": True,
                    "framework_match": True,
                    "language_match": True,
                    "warnings": [],
                    "recommendations": ["Repository is empty - will create new structure"]
                }

            # Extract file paths from the file data
            repo_files = [file_data["path"] for file_data in repo_files_data if isinstance(file_data, dict) and "path" in file_data]
            
            # Analyze framework compatibility
            framework_analysis = self._analyze_framework_compatibility(
                project_name, repo_files, expected_framework
            )

            # Analyze language compatibility
            language_analysis = self._analyze_language_compatibility(
                project_name, repo_files, expected_language
            )
            
            # Determine overall compatibility
            is_compatible = self._determine_compatibility(
                framework_analysis, language_analysis
            )
            
            result = {
                "is_compatible": is_compatible,
                "is_empty": False,
                "framework_match": framework_analysis["matches"],
                "language_match": language_analysis["matches"],
                "detected_frameworks": framework_analysis["detected"],
                "detected_languages": language_analysis["detected"],
                "confidence_score": self._calculate_confidence_score(
                    framework_analysis, language_analysis
                ),
                "warnings": [],
                "recommendations": []
            }
            
            # Add warnings and recommendations
            self._add_warnings_and_recommendations(
                result, framework_analysis, language_analysis, 
                expected_framework, expected_language
            )
            
            logger.info(f"✅ Repository validation completed: compatible={is_compatible}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Error validating repository compatibility: {e}")
            return {
                "is_compatible": True,  # Default to compatible on error
                "is_empty": False,
                "framework_match": False,
                "language_match": False,
                "error": str(e),
                "warnings": [f"Validation failed: {str(e)}"],
                "recommendations": ["Manual review recommended due to validation error"]
            }

    def _analyze_framework_compatibility(
        self,
        project_name: str,
        repo_files: List[str],
        expected_framework: AutomationFramework
    ) -> Dict[str, Any]:
        """Analyze if repository contains expected framework indicators."""
        detected_frameworks = []
        expected_indicators = self.framework_indicators.get(expected_framework, {})
        
        # Check for framework indicators
        for framework, indicators in self.framework_indicators.items():
            score = 0
            matches = []
            
            # Check file patterns
            for file_path in repo_files:
                file_name = os.path.basename(file_path).lower()
                
                # Check specific files
                for indicator_file in indicators.get('files', []):
                    if indicator_file.lower() in file_name:
                        score += 2
                        matches.append(f"File: {file_path}")
                
                # Check directory patterns
                for directory in indicators.get('directories', []):
                    if directory.lower() in file_path.lower():
                        score += 3
                        matches.append(f"Directory: {file_path}")
            
            # Read file contents for pattern matching (limited to avoid performance issues)
            content_matches = self._check_content_patterns(
                project_name, repo_files[:20], indicators.get('patterns', [])
            )
            score += len(content_matches) * 5
            matches.extend(content_matches)
            
            if score > 0:
                detected_frameworks.append({
                    "framework": framework,
                    "score": score,
                    "matches": matches
                })
        
        # Sort by score
        detected_frameworks.sort(key=lambda x: x["score"], reverse=True)
        
        # Check if expected framework is detected
        expected_detected = any(
            fw["framework"] == expected_framework 
            for fw in detected_frameworks
        )
        
        return {
            "matches": expected_detected,
            "detected": detected_frameworks,
            "expected": expected_framework,
            "confidence": detected_frameworks[0]["score"] if detected_frameworks else 0
        }

    def _analyze_language_compatibility(
        self,
        project_name: str,
        repo_files: List[str],
        expected_language: ProgrammingLanguage
    ) -> Dict[str, Any]:
        """Analyze if repository contains expected language indicators."""
        detected_languages = []
        
        # Check for language indicators
        for language, indicators in self.language_indicators.items():
            score = 0
            matches = []
            
            # Check file extensions
            for file_path in repo_files:
                file_ext = Path(file_path).suffix.lower()
                
                if file_ext in indicators.get('extensions', []):
                    score += 3
                    matches.append(f"Extension: {file_path}")
                
                # Check specific files
                file_name = os.path.basename(file_path).lower()
                for indicator_file in indicators.get('files', []):
                    if indicator_file.lower() in file_name:
                        score += 2
                        matches.append(f"File: {file_path}")
            
            # Check content patterns (limited files)
            content_matches = self._check_content_patterns(
                project_name, repo_files[:10], indicators.get('patterns', [])
            )
            score += len(content_matches) * 2
            matches.extend(content_matches)
            
            if score > 0:
                detected_languages.append({
                    "language": language,
                    "score": score,
                    "matches": matches
                })
        
        # Sort by score
        detected_languages.sort(key=lambda x: x["score"], reverse=True)
        
        # Check if expected language is detected
        expected_detected = any(
            lang["language"] == expected_language 
            for lang in detected_languages
        )
        
        return {
            "matches": expected_detected,
            "detected": detected_languages,
            "expected": expected_language,
            "confidence": detected_languages[0]["score"] if detected_languages else 0
        }

    def _check_content_patterns(
        self,
        project_name: str,
        file_paths: List[str],
        patterns: List[str]
    ) -> List[str]:
        """Check file contents for specific patterns."""
        matches = []
        
        # Limit to text files and reasonable size
        text_extensions = {'.py', '.js', '.ts', '.java', '.cs', '.txt', '.json', '.xml', '.yml', '.yaml'}
        
        for file_path in file_paths:
            try:
                file_ext = Path(file_path).suffix.lower()
                if file_ext not in text_extensions:
                    continue
                
                # Read file content using git service (limit size to prevent memory issues)
                content = git_service.read_file_content(project_name, file_path)
                if content and len(content) < 100000:  # 100KB limit
                    
                    for pattern in patterns:
                        if re.search(pattern, content, re.IGNORECASE):
                            matches.append(f"Pattern '{pattern}' in {file_path}")
                            break  # One match per file is enough
                            
            except Exception as e:
                logger.debug(f"Could not read {file_path}: {e}")
                continue
        
        return matches

    def _determine_compatibility(
        self, 
        framework_analysis: Dict[str, Any], 
        language_analysis: Dict[str, Any]
    ) -> bool:
        """Determine overall compatibility based on framework and language analysis."""
        # If both match, it's compatible
        if framework_analysis["matches"] and language_analysis["matches"]:
            return True
        
        # If no frameworks/languages detected, assume compatible (empty or generic repo)
        if not framework_analysis["detected"] and not language_analysis["detected"]:
            return True
        
        # If only one type is detected and it matches, it's compatible
        if framework_analysis["matches"] and not language_analysis["detected"]:
            return True
        if language_analysis["matches"] and not framework_analysis["detected"]:
            return True
        
        # If conflicting frameworks/languages detected, it's incompatible
        return False

    def _calculate_confidence_score(
        self, 
        framework_analysis: Dict[str, Any], 
        language_analysis: Dict[str, Any]
    ) -> float:
        """Calculate confidence score for the compatibility assessment."""
        framework_confidence = framework_analysis.get("confidence", 0)
        language_confidence = language_analysis.get("confidence", 0)
        
        # Normalize scores (assuming max score of 20 for each)
        framework_norm = min(framework_confidence / 20.0, 1.0)
        language_norm = min(language_confidence / 20.0, 1.0)
        
        # Average the normalized scores
        return (framework_norm + language_norm) / 2.0

    def _add_warnings_and_recommendations(
        self,
        result: Dict[str, Any],
        framework_analysis: Dict[str, Any],
        language_analysis: Dict[str, Any],
        expected_framework: AutomationFramework,
        expected_language: ProgrammingLanguage
    ):
        """Add warnings and recommendations to the result."""
        warnings = []
        recommendations = []
        
        # Framework warnings
        if not framework_analysis["matches"] and framework_analysis["detected"]:
            detected_fw = framework_analysis["detected"][0]["framework"]
            warnings.append(
                f"Repository appears to use {detected_fw.value} but {expected_framework.value} was selected"
            )
            recommendations.append(
                f"Consider changing project framework to {detected_fw.value} or verify repository contents"
            )
        
        # Language warnings
        if not language_analysis["matches"] and language_analysis["detected"]:
            detected_lang = language_analysis["detected"][0]["language"]
            warnings.append(
                f"Repository appears to use {detected_lang.value} but {expected_language.value} was selected"
            )
            recommendations.append(
                f"Consider changing project language to {detected_lang.value} or verify repository contents"
            )
        
        # Low confidence warning
        if result["confidence_score"] < 0.3:
            warnings.append("Low confidence in compatibility assessment")
            recommendations.append("Manual review of repository contents recommended")
        
        result["warnings"] = warnings
        result["recommendations"] = recommendations

# Global instance
repository_validation_service = RepositoryValidationService()
