import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional, AsyncGenerator
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)

class StreamingProgressStore:
    """In-memory store for streaming progress updates."""
    
    def __init__(self):
        self._streams: Dict[str, asyncio.Queue] = {}
        self._session_status: Dict[str, str] = {}
        self._session_created_time: Dict[str, float] = {}  # Track when streams were created
        self._delayed_cleanup_tasks: Dict[str, asyncio.Task] = {}  # Track delayed cleanup tasks
        self._cancelled_sessions: set = set()  # Track cancelled sessions
    
    def create_stream(self, session_id: str) -> None:
        """Create a new stream for a session."""
        if session_id not in self._streams:
            self._streams[session_id] = asyncio.Queue()
            self._session_status[session_id] = "active"
            self._session_created_time[session_id] = time.time()
            # Remove from cancelled sessions if it was there
            self._cancelled_sessions.discard(session_id)
            logger.info(f"Created stream for session {session_id}")
    
    async def add_progress(self, session_id: str, progress: Dict[str, Any]) -> None:
        """Add progress update to a session's stream."""
        # Check if session was cancelled
        if session_id in self._cancelled_sessions:
            logger.debug(f"Session {session_id} was cancelled - skipping progress update")
            return

        # Check if stream exists and is active
        if session_id not in self._streams:
            # Stream doesn't exist - this is normal if cleanup happened
            logger.debug(f"Stream {session_id} doesn't exist - likely cleaned up already")
            return

        if self._session_status.get(session_id) != "active":
            # Stream is not active - this is normal if cleanup happened or stream completed
            logger.debug(f"Stream {session_id} is not active (status: {self._session_status.get(session_id, 'unknown')}) - skipping progress update")
            return

        try:
            # Add timestamp if not present
            if "timestamp" not in progress:
                progress["timestamp"] = datetime.now().isoformat()

            # Double-check stream still exists (race condition protection)
            if session_id in self._streams:
                await self._streams[session_id].put(progress)

                # Only log important events, not every progress update
                if progress.get("type") in ["error", "complete", "explanation_complete", "review_complete_final", "code_chunk"]:
                    if progress.get("type") == "code_chunk":
                        chunk_preview = progress.get("data", {}).get("chunk", "")[:50] + "..." if progress.get("data", {}).get("chunk") else "no chunk"
                        logger.debug(f"Added code_chunk to stream {session_id}: {chunk_preview}")
                    else:
                        logger.debug(f"Added progress to stream {session_id}: {progress.get('type', 'unknown')} - {progress.get('message', '')}")

                # Schedule stream closure after explanation_complete with 2-minute delay
                if progress.get("type") == "explanation_complete":
                    logger.info(f"Code generation complete for {session_id}, scheduling stream closure in 2 minutes")
                    # Send a final completion message
                    if session_id in self._streams:  # Double-check again
                        await self._streams[session_id].put({
                            "type": "generation_complete",
                            "message": "Code generation completed successfully.",
                            "timestamp": datetime.now().isoformat()
                        })

                        # Schedule delayed closure
                        cleanup_task = asyncio.create_task(self._schedule_delayed_stream_closure(session_id, 120))  # 2 minutes
                        self._delayed_cleanup_tasks[session_id] = cleanup_task

                # Close stream on critical errors
                elif progress.get("type") in ["error"] or progress.get("status") in ["failed"]:
                    logger.info(f"Stream {session_id} completing with error: {progress.get('type')} status: {progress.get('status')}")
                    if session_id in self._streams:  # Double-check again
                        await self._streams[session_id].put({"type": "stream_end"})
                        self._session_status[session_id] = "completed"
            else:
                logger.debug(f"Stream {session_id} was cleaned up during progress update - this is normal")

        except Exception as e:
            # Don't log as error if it's just a closed stream
            if "closed" in str(e).lower() or "cancelled" in str(e).lower():
                logger.debug(f"Stream {session_id} was closed during progress update: {e}")
            else:
                logger.error(f"Error adding progress to stream {session_id}: {e}")
    
    async def get_progress_stream(self, session_id: str) -> AsyncGenerator[Dict[str, Any], None]:
        """Get progress stream for a session."""
        logger.info(f"GET_PROGRESS_STREAM CALLED for session {session_id}")
        if session_id not in self._streams:
            self.create_stream(session_id)

        queue = self._streams[session_id]
        logger.info(f"Stream status for {session_id}: {self._session_status.get(session_id)}")

        try:
            while self._session_status.get(session_id) == "active":
                try:
                    # Wait for progress with timeout
                    progress = await asyncio.wait_for(queue.get(), timeout=1.0)

                    if progress.get("type") == "stream_end":
                        logger.info(f"STREAM_END received for {session_id}")
                        break

                    yield progress
                    
                except asyncio.TimeoutError:
                    # Send keepalive
                    yield {
                        "type": "keepalive",
                        "timestamp": datetime.now().isoformat()
                    }
                    continue
                    
        except Exception as e:
            logger.error(f"Error in progress stream {session_id}: {e}")
            yield {
                "type": "error",
                "message": f"Stream error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        finally:
            # Don't auto-cleanup streams - let delayed cleanup or explicit cleanup handle it
            logger.debug(f"Stream connection ended for {session_id}, status: {self._session_status.get(session_id, 'unknown')}")
    
    def cleanup_stream(self, session_id: str, reason: str = "explicit_request") -> None:
        """Clean up stream resources for a session."""

        # Cancel any delayed cleanup tasks
        if session_id in self._delayed_cleanup_tasks:
            task = self._delayed_cleanup_tasks[session_id]
            if not task.done():
                task.cancel()
                logger.info(f"Cancelled delayed cleanup task for {session_id}")
            del self._delayed_cleanup_tasks[session_id]

        if session_id in self._streams:
            # Ensure the queue is properly closed
            try:
                queue = self._streams[session_id]
                # Put a final message to unblock any waiting consumers
                if not queue.empty():
                    try:
                        while not queue.empty():
                            queue.get_nowait()
                    except asyncio.QueueEmpty:
                        pass
            except Exception as e:
                logger.warning(f"Error cleaning up queue for session {session_id}: {e}")
            finally:
                del self._streams[session_id]

        if session_id in self._session_status:
            del self._session_status[session_id]
        if session_id in self._session_created_time:
            del self._session_created_time[session_id]
        # Keep cancelled sessions in the set for a while to prevent race conditions
        # They will be cleaned up by the periodic cleanup
        logger.info(f"Cleaned up stream for session {session_id} (reason: {reason})")

    def force_cleanup_stream(self, session_id: str) -> None:
        """Force cleanup of a stream (used for modal close, download, tab close)."""
        # Mark session as cancelled to prevent further progress updates
        self._cancelled_sessions.add(session_id)
        self.cleanup_stream(session_id, "forced_cleanup")

    def cancel_session(self, session_id: str) -> None:
        """Cancel a session to prevent further progress updates."""
        self._cancelled_sessions.add(session_id)
        logger.info(f"Session {session_id} marked as cancelled")

    def is_session_cancelled(self, session_id: str) -> bool:
        """Check if a session has been cancelled."""
        return session_id in self._cancelled_sessions

    def is_stream_active(self, session_id: str) -> bool:
        """Check if a stream is still active."""
        return session_id in self._streams and self._session_status.get(session_id) == "active"

    async def _schedule_delayed_stream_closure(self, session_id: str, delay_seconds: int):
        """Schedule delayed stream closure after successful completion."""
        try:
            logger.info(f"Scheduled stream closure for {session_id} in {delay_seconds} seconds")
            await asyncio.sleep(delay_seconds)

            # Check if stream is still active and hasn't been manually closed
            if self.is_stream_active(session_id):
                logger.info(f"Auto-closing stream {session_id} after {delay_seconds} second delay")

                # Send final closure message
                if session_id in self._streams:
                    await self._streams[session_id].put({
                        "type": "stream_end",
                        "message": f"Stream closed automatically after {delay_seconds//60} minutes",
                        "timestamp": datetime.now().isoformat()
                    })

                # Mark as completed and cleanup
                self._session_status[session_id] = "completed"
                self.cleanup_stream(session_id, "delayed_auto_closure")
            else:
                logger.info(f"Stream {session_id} already closed, skipping delayed closure")

        except asyncio.CancelledError:
            logger.info(f"Delayed closure cancelled for {session_id} (manual cleanup)")
            raise
        except Exception as e:
            logger.error(f"Error in delayed stream closure for {session_id}: {e}")
        finally:
            # Remove the task reference
            if session_id in self._delayed_cleanup_tasks:
                del self._delayed_cleanup_tasks[session_id]

    def cleanup_old_streams(self, max_age_hours: int = 24) -> int:
        """Clean up streams older than specified hours. Returns number of cleaned streams."""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        old_sessions = []

        for session_id, created_time in self._session_created_time.items():
            if current_time - created_time > max_age_seconds:
                old_sessions.append(session_id)

        for session_id in old_sessions:
            self.cleanup_stream(session_id, f"old_stream_{max_age_hours}h")

        # Also clean up old cancelled sessions that no longer have associated data
        old_cancelled_sessions = []

        # We don't have timestamps for cancelled sessions, so we'll clean them up based on
        # whether they still have any associated data
        for session_id in self._cancelled_sessions.copy():
            if (session_id not in self._streams and
                session_id not in self._session_status and
                session_id not in self._session_created_time):
                old_cancelled_sessions.append(session_id)

        for session_id in old_cancelled_sessions:
            self._cancelled_sessions.discard(session_id)

        total_cleaned = len(old_sessions) + len(old_cancelled_sessions)
        if total_cleaned > 0:
            logger.info(f"Cleaned up {len(old_sessions)} old streams and {len(old_cancelled_sessions)} old cancelled sessions")

        return total_cleaned

    def get_active_streams(self) -> int:
        """Get number of active streams."""
        return len([s for s in self._session_status.values() if s == "active"])

    def cleanup_all_streams(self) -> None:
        """Clean up all active streams - useful for shutdown."""
        session_ids = list(self._streams.keys())
        for session_id in session_ids:
            self.cleanup_stream(session_id)
        logger.info(f"Cleaned up all {len(session_ids)} active streams")

# Global instance
streaming_store = StreamingProgressStore()
