import uuid
import ast
import json
import re
from typing import List, Dict, Any, Op<PERSON>, Tu<PERSON>, Set
from pathlib import Path
import logging
from dataclasses import dataclass
from qdrant_client import QdrantClient, models
from sentence_transformers import SentenceTransformer
from app.services.git_service import git_service
from app.models.project import ProgrammingLanguage

logger = logging.getLogger(__name__)

@dataclass
class CodeEntity:
    """Represents a code entity (function, class, method, etc.)"""
    name: str
    type: str  # 'function', 'class', 'method', 'variable'
    file_path: str
    start_line: int
    end_line: int
    signature: str
    docstring: Optional[str]
    dependencies: List[str]
    complexity_score: float
    semantic_tags: List[str]

@dataclass
class TestCaseMapping:
    """Represents mapping between test cases and code entities"""
    test_case_id: str
    test_method_name: str
    target_entities: List[CodeEntity]
    coverage_score: float
    semantic_similarity: float
    implementation_status: str  # 'implemented', 'partial', 'missing'

@dataclass
class AgenticPlan:
    """Represents an agentic code generation plan"""
    requirement_id: str
    requirement_name: str
    analysis_summary: str
    generation_strategy: str
    target_files: List[str]
    dependencies: List[str]
    implementation_steps: List[Dict[str, Any]]
    confidence_score: float
    risk_assessment: Dict[str, Any]

class CodeEmbeddingService:
    """Production-grade agentic code embedding and intelligence service."""

    def __init__(self, qdrant_url: str = "http://localhost:6333"):
        """
        Initialize the Code Embedding Service.

        Args:
            qdrant_url: URL of the Qdrant vector database
        """
        self.qdrant_url = qdrant_url
        self.client = None
        self.encoder = None
        self.collection_name = "code_embeddings"
        self.is_available = False

        # Agentic intelligence caches
        self._code_entities_cache: Dict[str, List[CodeEntity]] = {}
        self._dependency_graph_cache: Dict[str, Dict[str, List[str]]] = {}
        self._test_mappings_cache: Dict[str, List[TestCaseMapping]] = {}

        self._initialize_services()

    def _initialize_services(self):
        """Initialize Qdrant client and encoder with error handling."""
        try:
            # Initialize Qdrant client
            self.client = QdrantClient(url=self.qdrant_url)

            # Test connection
            self.client.get_collections()

            # Initialize encoder
            self.encoder = SentenceTransformer('all-MiniLM-L6-v2')

            # Ensure collection exists
            self._ensure_collection_exists()

            self.is_available = True
            logger.info("Code embedding service initialized successfully")

        except Exception as e:
            logger.warning(f"Code embedding service unavailable: {e}")
            logger.info("Code embedding features will be disabled until Qdrant is available")
            self.is_available = False
            self.client = None
            self.encoder = None

    def _ensure_collection_exists(self):
        """Ensure the code embeddings collection exists."""
        if not self.is_available or not self.client:
            return

        try:
            # First try to get collection info directly
            try:
                self.client.get_collection(self.collection_name)
                logger.debug(f"Collection {self.collection_name} already exists")
                return
            except Exception:
                # Collection doesn't exist, create it
                pass

            # Create the collection
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=models.VectorParams(
                    size=384,  # all-MiniLM-L6-v2 embedding size
                    distance=models.Distance.COSINE
                )
            )
            logger.info(f"✅ Created collection: {self.collection_name}")

        except Exception as e:
            error_msg = str(e)
            if "already exists" in error_msg.lower():
                logger.debug(f"Collection {self.collection_name} already exists")
            else:
                logger.error(f"❌ Error ensuring collection exists: {e}")
                self.is_available = False

    def health_check(self) -> bool:
        """Check if the service is available and healthy."""
        if not self.is_available:
            return False

        try:
            if self.client:
                self.client.get_collections()
                return True
        except Exception as e:
            logger.warning(f"Code embedding service health check failed: {e}")
            self.is_available = False

        return False

    def retry_initialization(self) -> bool:
        """Retry initializing the service if it was previously unavailable."""
        if not self.is_available:
            logger.info("Retrying code embedding service initialization...")
            self._initialize_services()
        return self.is_available

    def embed_repository(self, project_id: int, project_name: str, language: ProgrammingLanguage) -> Dict[str, Any]:
        """
        Embed all code files from a repository into the vector database.

        Args:
            project_id: Unique ID of the project (primary key)
            project_name: Name of the project (for git operations)
            language: Programming language of the project

        Returns:
            Dictionary with embedding results
        """
        # Check if service is available
        if not self.is_available:
            logger.warning("Code embedding service unavailable, skipping repository embedding")
            return {
                "success": False,
                "error": "Code embedding service unavailable (Qdrant not connected)",
                "embedded_files": 0,
                "skipped_files": 0,
                "total_files": 0,
                "errors": []
            }

        try:
            # Get all repository files
            repo_files = git_service.get_repository_files(project_name)
            
            embedded_files = 0
            skipped_files = 0
            errors = []
            
            for file_info in repo_files:
                if not file_info['is_text'] or not self._should_embed_file(file_info['path'], language):
                    skipped_files += 1
                    continue
                
                try:
                    # Read file content
                    content = git_service.read_file_content(project_name, file_info['path'])
                    if not content or len(content.strip()) == 0:
                        skipped_files += 1
                        continue
                    
                    # Split content into chunks if it's too large
                    chunks = self._split_code_into_chunks(content, file_info['path'])

                    for i, chunk in enumerate(chunks):
                        self._embed_code_chunk(
                            project_id=project_id,
                            project_name=project_name,
                            file_path=file_info['path'],
                            chunk_index=i,
                            content=chunk,
                            language=language,
                            file_size=file_info['size']
                        )
                    
                    embedded_files += 1
                    
                except Exception as e:
                    error_msg = f"Error embedding file {file_info['path']}: {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg)
            
            result = {
                "success": True,
                "embedded_files": embedded_files,
                "skipped_files": skipped_files,
                "total_files": len(repo_files),
                "errors": errors
            }
            
            logger.info(f"Repository embedding completed: {embedded_files} files embedded, {skipped_files} skipped")
            return result
            
        except Exception as e:
            error_msg = f"Error embedding repository: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "embedded_files": 0,
                "skipped_files": 0,
                "total_files": 0,
                "errors": [error_msg]
            }
    
    def _should_embed_file(self, file_path: str, language: ProgrammingLanguage) -> bool:
        """Check if a file should be embedded based on its type and language."""
        
        # Skip certain directories and files
        skip_patterns = [
            '.git/', 'node_modules/', '__pycache__/', '.pytest_cache/',
            'target/', 'bin/', 'obj/', '.vscode/', '.idea/',
            '.log', '.tmp', '.cache'
        ]

        # Skip documentation and non-code files
        skip_file_extensions = [
            '.md', '.txt', '.rst', '.pdf', '.doc', '.docx',
            '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico',
            '.zip', '.tar', '.gz', '.rar', '.7z',
            '.json', '.xml', '.yml', '.yaml', '.ini', '.cfg', '.conf'
        ]
        
        for pattern in skip_patterns:
            if pattern in file_path:
                return False

        # Skip files with non-code extensions
        file_lower = file_path.lower()
        for ext in skip_file_extensions:
            if file_lower.endswith(ext):
                return False
        
        # Define file extensions to embed for each language
        extensions_map = {
            ProgrammingLanguage.PYTHON: ['.py'],
            ProgrammingLanguage.JAVASCRIPT: ['.js', '.ts', '.jsx', '.tsx'],
            ProgrammingLanguage.JAVA: ['.java'],
            ProgrammingLanguage.CSHARP: ['.cs']
        }
        
        # Also include common config and documentation files
        common_extensions = ['.json', '.yaml', '.yml', '.xml', '.md', '.txt', '.properties', '.ini']
        
        valid_extensions = extensions_map.get(language, []) + common_extensions
        
        return any(file_path.lower().endswith(ext) for ext in valid_extensions)
    
    def _split_code_into_chunks(self, content: str, file_path: str, max_chunk_size: int = 2000) -> List[str]:
        """Split code content into manageable chunks for embedding."""
        
        # If content is small enough, return as single chunk
        if len(content) <= max_chunk_size:
            return [content]
        
        chunks = []
        
        # Try to split by functions/classes first
        if self._is_code_file(file_path):
            code_chunks = self._split_by_code_structure(content, file_path)
            if code_chunks:
                # Further split large chunks if needed
                for chunk in code_chunks:
                    if len(chunk) <= max_chunk_size:
                        chunks.append(chunk)
                    else:
                        # Split large chunks by lines
                        chunks.extend(self._split_by_lines(chunk, max_chunk_size))
                return chunks
        
        # Fallback: split by lines
        return self._split_by_lines(content, max_chunk_size)
    
    def _is_code_file(self, file_path: str) -> bool:
        """Check if a file is a code file (not config/documentation)."""
        code_extensions = ['.py', '.js', '.ts', '.java', '.cs', '.jsx', '.tsx']
        return any(file_path.lower().endswith(ext) for ext in code_extensions)
    
    def _split_by_code_structure(self, content: str, file_path: str) -> List[str]:
        """Split code by functions, classes, or other logical structures."""
        
        # This is a simplified implementation
        # In a production system, you might use AST parsing for better results
        
        lines = content.split('\n')
        chunks = []
        current_chunk = []
        indent_level = 0
        
        for line in lines:
            stripped_line = line.strip()
            
            # Detect function/class definitions (simplified)
            if (stripped_line.startswith('def ') or 
                stripped_line.startswith('class ') or
                stripped_line.startswith('function ') or
                stripped_line.startswith('public ') or
                stripped_line.startswith('private ') or
                stripped_line.startswith('protected ')):
                
                # Save previous chunk if it exists
                if current_chunk:
                    chunks.append('\n'.join(current_chunk))
                    current_chunk = []
                
                indent_level = len(line) - len(line.lstrip())
            
            current_chunk.append(line)
            
            # If we have a substantial chunk, consider splitting
            if len('\n'.join(current_chunk)) > 1500 and stripped_line == '':
                chunks.append('\n'.join(current_chunk))
                current_chunk = []
        
        # Add remaining chunk
        if current_chunk:
            chunks.append('\n'.join(current_chunk))
        
        return [chunk for chunk in chunks if chunk.strip()]
    
    def _split_by_lines(self, content: str, max_chunk_size: int) -> List[str]:
        """Split content by lines, respecting the maximum chunk size."""
        
        lines = content.split('\n')
        chunks = []
        current_chunk = []
        current_size = 0
        
        for line in lines:
            line_size = len(line) + 1  # +1 for newline
            
            if current_size + line_size > max_chunk_size and current_chunk:
                chunks.append('\n'.join(current_chunk))
                current_chunk = [line]
                current_size = line_size
            else:
                current_chunk.append(line)
                current_size += line_size
        
        if current_chunk:
            chunks.append('\n'.join(current_chunk))
        
        return chunks
    
    def _embed_code_chunk(
        self,
        project_id: int,
        project_name: str,
        file_path: str,
        chunk_index: int,
        content: str,
        language: ProgrammingLanguage,
        file_size: int
    ):
        """Embed a single code chunk into the vector database."""

        if not self.is_available or not self.client or not self.encoder:
            raise Exception("Code embedding service is not available")

        try:
            # Generate embedding
            embedding = self.encoder.encode(content).tolist()

            # Prepare metadata with unique project ID
            payload = {
                "project_id": project_id,
                "project_name": project_name,  # Keep for backward compatibility and debugging
                "file_path": file_path,
                "chunk_index": chunk_index,
                "content": content,
                "language": language.value,
                "file_size": file_size,
                "content_type": "code",
                "chunk_size": len(content)
            }

            # Add to Qdrant
            self.client.upsert(
                collection_name=self.collection_name,
                points=[
                    models.PointStruct(
                        id=str(uuid.uuid4()),
                        vector=embedding,
                        payload=payload
                    )
                ]
            )

        except Exception as e:
            error_msg = str(e)
            # Check if collection doesn't exist and try to create it
            if "doesn't exist" in error_msg or "Not found" in error_msg:
                logger.warning(f"Collection {self.collection_name} doesn't exist, attempting to create it")
                try:
                    self._ensure_collection_exists()
                    # Retry the embedding after creating collection
                    self.client.upsert(
                        collection_name=self.collection_name,
                        points=[
                            models.PointStruct(
                                id=str(uuid.uuid4()),
                                vector=embedding,
                                payload=payload
                            )
                        ]
                    )
                    logger.info(f"Successfully embedded chunk from {file_path} after creating collection")
                except Exception as retry_error:
                    logger.error(f"Failed to embed chunk from {file_path} even after creating collection: {retry_error}")
            else:
                logger.error(f"Error embedding code chunk from {file_path}: {e}")
    
    def search_similar_code(
        self,
        query: str,
        project_id: int,
        language: Optional[ProgrammingLanguage] = None,
        limit: int = 10,
        score_threshold: float = 0.6
    ) -> List[Dict[str, Any]]:
        """
        Search for similar code in the embedded repository.

        Args:
            query: Search query (natural language or code)
            project_id: Unique ID of the project to search in
            language: Programming language filter
            limit: Maximum number of results
            score_threshold: Minimum similarity score

        Returns:
            List of similar code chunks with metadata
        """
        try:
            # Generate embedding for query
            query_embedding = self.encoder.encode(query).tolist()
            
            # Prepare filter using unique project ID
            filter_conditions = [
                models.FieldCondition(
                    key="project_id",
                    match=models.MatchValue(value=project_id)
                )
            ]
            
            if language:
                filter_conditions.append(
                    models.FieldCondition(
                        key="language",
                        match=models.MatchValue(value=language.value)
                    )
                )
            
            search_filter = models.Filter(must=filter_conditions)
            
            # Search
            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                query_filter=search_filter,
                limit=limit,
                score_threshold=score_threshold
            )
            
            # Format results
            results = []
            for point in search_result:
                results.append({
                    "file_path": point.payload["file_path"],
                    "content": point.payload["content"],
                    "language": point.payload["language"],
                    "score": point.score,
                    "chunk_index": point.payload["chunk_index"],
                    "file_size": point.payload["file_size"]
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching similar code: {e}")
            return []
    
    def get_code_context_for_generation(
        self,
        project_id: int,
        requirement_description: str,
        language: ProgrammingLanguage,
        limit: int = 5
    ) -> str:
        """
        Get relevant code context for code generation.

        Args:
            project_id: Unique ID of the project
            requirement_description: Description of the requirement
            language: Programming language
            limit: Maximum number of code examples

        Returns:
            Formatted code context string
        """
        similar_code = self.search_similar_code(
            query=requirement_description,
            project_id=project_id,
            language=language,
            limit=limit
        )

        if not similar_code:
            return ""

        context_parts = ["Relevant code examples from the existing codebase:"]

        for i, code_info in enumerate(similar_code, 1):
            context_parts.append(f"\n{i}. File: {code_info['file_path']} (similarity: {code_info['score']:.2f})")
            context_parts.append(f"```{language.value}")
            context_parts.append(code_info['content'][:1000])  # Limit content size
            context_parts.append("```")

        return "\n".join(context_parts)

    # ==================== AGENTIC INTELLIGENCE METHODS ====================

    async def create_agentic_generation_plan(
        self,
        project_name: str,
        requirement_name: str,
        requirement_description: str,
        test_cases: List[Any],
        page_elements: Dict[str, Any],
        language: ProgrammingLanguage
    ) -> AgenticPlan:
        """
        Create a comprehensive agentic plan for code generation using deep analysis.
        This is the core intelligence method that mimics how agentic IDEs work.
        """
        logger.info(f"🧠 Creating agentic generation plan for requirement: {requirement_name}")

        try:
            # Circuit breaker: Check if we've had too many failures recently
            if hasattr(self, '_agentic_failure_count'):
                if self._agentic_failure_count > 3:
                    logger.warning("🚨 Circuit breaker activated - too many agentic failures, using lightweight plan")
                    return await self._create_lightweight_agentic_plan(
                        project_name, requirement_name, requirement_description, test_cases, page_elements, language
                    )
            else:
                self._agentic_failure_count = 0

            # First, check if we need comprehensive analysis or can use lightweight approach
            from app.services.git_service import git_service
            repo_files = git_service.get_repository_files(project_name)

            # If repository is empty or has very few files, skip comprehensive analysis
            if len(repo_files) < 5:
                logger.info("🚀 Repository has few files, using lightweight agentic analysis")
                return await self._create_lightweight_agentic_plan(
                    project_name, requirement_name, requirement_description, test_cases, page_elements, language
                )

            # Step 1: Deep code analysis and entity extraction (limited scope)
            logger.info(f"🔍 Extracting code entities for project: {project_name} (limited scope)")
            code_entities = await self._extract_code_entities_limited(project_name, language, requirement_name)

            # Step 2: Build dependency graph
            dependency_graph = await self._build_dependency_graph(code_entities)

            # Step 3: Analyze test-code mappings
            test_mappings = await self._analyze_test_code_mappings(
                test_cases, code_entities, requirement_name
            )

            # Step 4: Semantic requirement analysis
            requirement_analysis = await self._analyze_requirement_semantics(
                requirement_name, requirement_description, test_cases, page_elements
            )

            # Step 5: Generate implementation strategy
            strategy = await self._generate_implementation_strategy(
                requirement_analysis, code_entities, test_mappings, dependency_graph
            )

            # Step 6: Create execution plan
            execution_plan = await self._create_execution_plan(
                strategy, test_cases, code_entities, page_elements
            )

            # Step 7: Risk assessment
            risk_assessment = await self._assess_implementation_risks(
                execution_plan, code_entities, dependency_graph
            )

            plan = AgenticPlan(
                requirement_id=str(hash(requirement_name)),
                requirement_name=requirement_name,
                analysis_summary=requirement_analysis.get('summary', ''),
                generation_strategy=strategy.get('approach', 'incremental'),
                target_files=strategy.get('target_files', []),
                dependencies=strategy.get('dependencies', []),
                implementation_steps=execution_plan,
                confidence_score=strategy.get('confidence', 0.8),
                risk_assessment=risk_assessment
            )

            logger.info(f"✅ Agentic plan created with {len(execution_plan)} steps, confidence: {plan.confidence_score:.2f}")
            return plan

        except Exception as e:
            logger.error(f"❌ Failed to create agentic plan: {e}")
            # Increment failure count for circuit breaker
            if hasattr(self, '_agentic_failure_count'):
                self._agentic_failure_count += 1
            else:
                self._agentic_failure_count = 1

            # Fallback to basic plan
            return self._create_fallback_plan(requirement_name, test_cases)

    async def _create_lightweight_agentic_plan(
        self,
        project_name: str,
        requirement_name: str,
        requirement_description: str,
        test_cases: List[Any],
        page_elements: Dict[str, Any],
        language: ProgrammingLanguage
    ) -> AgenticPlan:
        """Create a lightweight agentic plan for simple projects or new file creation."""
        logger.info(f"🚀 Creating lightweight agentic plan for: {requirement_name}")

        try:
            # Simple analysis without deep entity extraction
            suggested_files = []

            # Determine file naming based on requirement
            clean_name = requirement_name.lower().replace(' ', '_').replace('-', '_')

            if language == ProgrammingLanguage.JAVASCRIPT:
                test_file = f"tests/{clean_name}Test.js"
                page_file = f"pages/{clean_name}Page.js"
            elif language == ProgrammingLanguage.PYTHON:
                test_file = f"tests/test_{clean_name}.py"
                page_file = f"pages/{clean_name}_page.py"
            else:
                test_file = f"tests/{clean_name}Test.js"
                page_file = f"pages/{clean_name}Page.js"

            suggested_files.extend([test_file, page_file])

            return AgenticPlan(
                requirement_id=str(hash(requirement_name)),
                requirement_name=requirement_name,
                analysis_summary=f"Lightweight analysis for {requirement_name}",
                generation_strategy="create_new_file",
                target_files=suggested_files,
                dependencies=[],
                implementation_steps=[
                    {
                        "step": "Create test file",
                        "action": "create_file",
                        "priority": "high",
                        "estimated_effort": "medium",
                        "file_path": test_file
                    },
                    {
                        "step": "Create page object",
                        "action": "create_file",
                        "priority": "medium",
                        "estimated_effort": "low",
                        "file_path": page_file
                    }
                ],
                confidence_score=0.8,  # High confidence for simple cases
                risk_assessment={
                    "complexity_risks": ["low_complexity"],
                    "integration_risks": ["minimal_integration"],
                    "maintenance_risks": ["standard_maintenance"]
                }
            )

        except Exception as e:
            logger.error(f"❌ Failed to create lightweight agentic plan: {e}")
            return AgenticPlan(
                requirement_id=str(hash(requirement_name)),
                requirement_name=requirement_name,
                analysis_summary=f"Fallback analysis for {requirement_name}",
                generation_strategy="incremental",
                target_files=[],
                dependencies=[],
                implementation_steps=[],
                confidence_score=0.3,
                risk_assessment={
                    "complexity_risks": ["unknown_complexity"],
                    "integration_risks": ["unknown_integration"],
                    "maintenance_risks": ["unknown_maintenance"]
                }
            )

    async def _extract_code_entities_limited(self, project_name: str, language: ProgrammingLanguage, requirement_name: str) -> List[CodeEntity]:
        """Extract code entities with limited scope to avoid analyzing irrelevant files."""
        logger.info(f"🔍 Limited entity extraction for requirement: {requirement_name}")

        try:
            from app.services.git_service import git_service
            repo_files = git_service.get_repository_files(project_name)

            # Filter files to only analyze relevant ones
            relevant_files = []
            requirement_keywords = requirement_name.lower().split()

            for file_info in repo_files:
                # Extract file path from the dictionary
                file_path = file_info.get('path', '') if isinstance(file_info, dict) else str(file_info)
                file_path_lower = file_path.lower()

                # Skip files that are clearly unrelated to the requirement
                is_relevant = False

                # Check if file contains requirement keywords
                for keyword in requirement_keywords:
                    if keyword in file_path_lower:
                        is_relevant = True
                        break

                # Always include test files and page objects for analysis
                if any(pattern in file_path_lower for pattern in ['test', 'spec', 'page']):
                    is_relevant = True

                if is_relevant:
                    relevant_files.append(file_path)

            logger.info(f"🎯 Analyzing relevant files only!")

            # Extract entities only from relevant files
            all_entities = []
            for file_path in relevant_files[:10]:  # Limit to first 10 relevant files
                try:
                    # Read file content first
                    file_content = git_service.read_file_content(project_name, file_path)
                    if not file_content:
                        continue

                    entities = await self._extract_entities_from_file(file_content, file_path, language)
                    all_entities.extend(entities)
                except Exception as e:
                    logger.warning(f"⚠️ Failed to extract entities from {file_path}: {e}")
                    continue

            logger.info(f"✅ Extracted entities from relevant files")
            return all_entities

        except Exception as e:
            logger.error(f"❌ Limited entity extraction failed: {e}")
            return []

    async def _extract_code_entities(self, project_name: str, language: ProgrammingLanguage) -> List[CodeEntity]:
        """Extract and analyze all code entities with deep semantic understanding."""
        if project_name in self._code_entities_cache:
            return self._code_entities_cache[project_name]

        logger.info(f"🔍 Extracting code entities for project: {project_name}")
        entities = []

        try:
            from app.services.git_service import git_service
            repo_files = git_service.get_repository_files(project_name)

            for file_info in repo_files:
                if not file_info['is_text'] or not self._is_code_file(file_info['path']):
                    continue

                file_path = file_info['path']
                file_content = git_service.read_file_content(project_name, file_path)

                if not file_content:
                    continue

                # Extract entities based on language
                file_entities = await self._extract_entities_from_file(
                    file_content, file_path, language
                )
                entities.extend(file_entities)

            # Cache the results
            self._code_entities_cache[project_name] = entities
            logger.info(f"📊 Extracted {len(entities)} code entities")
            return entities

        except Exception as e:
            logger.error(f"❌ Failed to extract code entities: {e}")
            return []

    async def _extract_entities_from_file(
        self,
        file_content: str,
        file_path: str,
        language: ProgrammingLanguage
    ) -> List[CodeEntity]:
        """Extract code entities from a single file using AST parsing and LLM analysis."""
        entities = []

        try:
            if language == ProgrammingLanguage.PYTHON:
                entities = await self._extract_python_entities(file_content, file_path)
            elif language == ProgrammingLanguage.JAVASCRIPT:
                entities = await self._extract_javascript_entities(file_content, file_path)
            elif language == ProgrammingLanguage.JAVA:
                entities = await self._extract_java_entities(file_content, file_path)
            else:
                # Fallback to regex-based extraction
                entities = await self._extract_generic_entities(file_content, file_path)

            # Enhance entities with semantic analysis
            enhanced_entities = await self._enhance_entities_with_semantics(entities, file_content)
            return enhanced_entities

        except Exception as e:
            logger.warning(f"⚠️ Failed to extract entities from {file_path}: {e}")
            return []

    async def _extract_python_entities(self, file_content: str, file_path: str) -> List[CodeEntity]:
        """Extract Python entities using AST parsing."""
        entities = []

        try:
            tree = ast.parse(file_content)

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    entities.append(CodeEntity(
                        name=node.name,
                        type='function',
                        file_path=file_path,
                        start_line=node.lineno,
                        end_line=getattr(node, 'end_lineno', node.lineno),
                        signature=self._extract_function_signature(node),
                        docstring=ast.get_docstring(node),
                        dependencies=self._extract_function_dependencies(node),
                        complexity_score=self._calculate_complexity(node),
                        semantic_tags=[]
                    ))
                elif isinstance(node, ast.ClassDef):
                    entities.append(CodeEntity(
                        name=node.name,
                        type='class',
                        file_path=file_path,
                        start_line=node.lineno,
                        end_line=getattr(node, 'end_lineno', node.lineno),
                        signature=f"class {node.name}",
                        docstring=ast.get_docstring(node),
                        dependencies=self._extract_class_dependencies(node),
                        complexity_score=self._calculate_complexity(node),
                        semantic_tags=[]
                    ))

            return entities

        except SyntaxError as e:
            logger.warning(f"⚠️ Syntax error in {file_path}: {e}")
            return []
        except Exception as e:
            logger.warning(f"⚠️ Failed to parse Python file {file_path}: {e}")
            return []

    async def _extract_javascript_entities(self, file_content: str, file_path: str) -> List[CodeEntity]:
        """Extract JavaScript entities using regex patterns (simplified AST alternative)."""
        entities = []

        try:
            # Function declarations
            function_pattern = r'(?:async\s+)?(?:function\s+(\w+)|(\w+)\s*[:=]\s*(?:async\s+)?function|\b(\w+)\s*\([^)]*\)\s*(?:=>|{))'
            for match in re.finditer(function_pattern, file_content, re.MULTILINE):
                func_name = match.group(1) or match.group(2) or match.group(3)
                if func_name and not func_name in ['if', 'for', 'while', 'switch']:
                    start_line = file_content[:match.start()].count('\n') + 1
                    entities.append(CodeEntity(
                        name=func_name,
                        type='function',
                        file_path=file_path,
                        start_line=start_line,
                        end_line=start_line + 10,  # Approximate
                        signature=match.group(0)[:100],
                        docstring=None,
                        dependencies=[],
                        complexity_score=1.0,
                        semantic_tags=[]
                    ))

            # Class declarations
            class_pattern = r'class\s+(\w+)(?:\s+extends\s+\w+)?\s*{'
            for match in re.finditer(class_pattern, file_content, re.MULTILINE):
                class_name = match.group(1)
                start_line = file_content[:match.start()].count('\n') + 1
                entities.append(CodeEntity(
                    name=class_name,
                    type='class',
                    file_path=file_path,
                    start_line=start_line,
                    end_line=start_line + 20,  # Approximate
                    signature=match.group(0),
                    docstring=None,
                    dependencies=[],
                    complexity_score=2.0,
                    semantic_tags=[]
                ))

            return entities

        except Exception as e:
            logger.warning(f"⚠️ Failed to extract JavaScript entities from {file_path}: {e}")
            return []

    def _extract_function_signature(self, node: ast.FunctionDef) -> str:
        """Extract function signature from AST node."""
        try:
            args = []
            for arg in node.args.args:
                args.append(arg.arg)
            return f"{node.name}({', '.join(args)})"
        except Exception:
            return f"{node.name}(...)"

    def _extract_function_dependencies(self, node: ast.FunctionDef) -> List[str]:
        """Extract function dependencies from AST node."""
        dependencies = []
        try:
            for child in ast.walk(node):
                if isinstance(child, ast.Call):
                    if isinstance(child.func, ast.Name):
                        dependencies.append(child.func.id)
                    elif isinstance(child.func, ast.Attribute):
                        dependencies.append(child.func.attr)
        except Exception:
            pass
        return list(set(dependencies))[:10]  # Limit to 10 dependencies

    def _extract_class_dependencies(self, node: ast.ClassDef) -> List[str]:
        """Extract class dependencies from AST node."""
        dependencies = []
        try:
            # Extract base classes
            for base in node.bases:
                if isinstance(base, ast.Name):
                    dependencies.append(base.id)

            # Extract method calls and attributes
            for child in ast.walk(node):
                if isinstance(child, ast.Call):
                    if isinstance(child.func, ast.Name):
                        dependencies.append(child.func.id)
                    elif isinstance(child.func, ast.Attribute):
                        dependencies.append(child.func.attr)
        except Exception:
            pass
        return list(set(dependencies))[:10]  # Limit to 10 dependencies

    def _calculate_complexity(self, node) -> float:
        """Calculate complexity score for AST node."""
        try:
            complexity = 1.0

            # Count control flow statements
            for child in ast.walk(node):
                if isinstance(child, (ast.If, ast.For, ast.While, ast.Try)):
                    complexity += 0.5
                elif isinstance(child, ast.FunctionDef):
                    complexity += 0.3
                elif isinstance(child, ast.ClassDef):
                    complexity += 0.7

            return min(complexity, 10.0)  # Cap at 10.0
        except Exception:
            return 1.0

    async def _extract_java_entities(self, file_content: str, file_path: str) -> List[CodeEntity]:
        """Extract Java entities using regex patterns (simplified AST alternative)."""
        entities = []

        try:
            # Class declarations
            class_pattern = r'(?:public\s+|private\s+|protected\s+)?class\s+(\w+)(?:\s+extends\s+\w+)?(?:\s+implements\s+[\w,\s]+)?\s*{'
            for match in re.finditer(class_pattern, file_content, re.MULTILINE):
                class_name = match.group(1)
                start_line = file_content[:match.start()].count('\n') + 1
                entities.append(CodeEntity(
                    name=class_name,
                    type='class',
                    file_path=file_path,
                    start_line=start_line,
                    end_line=start_line + 20,  # Approximate
                    signature=match.group(0),
                    docstring=None,
                    dependencies=[],
                    complexity_score=2.0,
                    semantic_tags=[]
                ))

            # Method declarations
            method_pattern = r'(?:public\s+|private\s+|protected\s+)?(?:static\s+)?(?:\w+\s+)?(\w+)\s*\([^)]*\)\s*(?:throws\s+[\w,\s]+)?\s*{'
            for match in re.finditer(method_pattern, file_content, re.MULTILINE):
                method_name = match.group(1)
                if method_name not in ['if', 'for', 'while', 'switch', 'try']:
                    start_line = file_content[:match.start()].count('\n') + 1
                    entities.append(CodeEntity(
                        name=method_name,
                        type='method',
                        file_path=file_path,
                        start_line=start_line,
                        end_line=start_line + 10,  # Approximate
                        signature=match.group(0)[:100],
                        docstring=None,
                        dependencies=[],
                        complexity_score=1.5,
                        semantic_tags=[]
                    ))

            return entities

        except Exception as e:
            logger.warning(f"⚠️ Failed to extract Java entities from {file_path}: {e}")
            return []

    async def _extract_generic_entities(self, file_content: str, file_path: str) -> List[CodeEntity]:
        """Extract entities using generic regex patterns."""
        entities = []

        try:
            # Generic function pattern
            function_pattern = r'(?:def\s+|function\s+|func\s+)(\w+)\s*\('
            for match in re.finditer(function_pattern, file_content, re.MULTILINE):
                func_name = match.group(1)
                start_line = file_content[:match.start()].count('\n') + 1
                entities.append(CodeEntity(
                    name=func_name,
                    type='function',
                    file_path=file_path,
                    start_line=start_line,
                    end_line=start_line + 5,  # Approximate
                    signature=match.group(0),
                    docstring=None,
                    dependencies=[],
                    complexity_score=1.0,
                    semantic_tags=[]
                ))

            return entities

        except Exception as e:
            logger.warning(f"⚠️ Failed to extract generic entities from {file_path}: {e}")
            return []

    async def _enhance_entities_with_semantics(self, entities: List[CodeEntity], file_content: str) -> List[CodeEntity]:
        """Enhance entities with semantic analysis using LLM."""
        if not entities or not self.is_available:
            return entities

        try:
            from app.services.llm_service import llm_service

            # Analyze entities in batches with strict limits to prevent loops
            enhanced_entities = []
            max_entities_to_analyze = min(5, len(entities))  # Reduce from 10 to 5 to prevent loops

            logger.info(f"🔍 Enhancing {max_entities_to_analyze} entities with semantic analysis")

            for i, entity in enumerate(entities[:max_entities_to_analyze]):
                try:
                    # Extract entity code snippet with size limit
                    lines = file_content.split('\n')
                    start_idx = max(0, entity.start_line - 1)
                    end_idx = min(len(lines), entity.end_line, start_idx + 20)  # Limit to 20 lines max
                    entity_code = '\n'.join(lines[start_idx:end_idx])

                    # Skip if code is too large or empty
                    if len(entity_code) > 1000 or len(entity_code.strip()) < 10:
                        logger.debug(f"⏭️ Skipping entity {entity.name} - code too large or too small")
                        enhanced_entities.append(entity)
                        continue

                    # Analyze with LLM with timeout and retry logic
                    logger.debug(f"🔍 Analyzing entity {i+1}/{max_entities_to_analyze}: {entity.name}")
                    analysis = await self._analyze_entity_with_timeout(
                        llm_service, entity_code, entity.file_path, entity.name
                    )

                    # Update entity with semantic tags
                    if analysis and isinstance(analysis, dict):
                        entity.semantic_tags = analysis.get('semantic_tags', [])
                    else:
                        entity.semantic_tags = ['analysis_failed']

                    enhanced_entities.append(entity)

                except Exception as e:
                    logger.warning(f"⚠️ Failed to enhance entity {entity.name}: {e}")
                    entity.semantic_tags = ['analysis_error']
                    enhanced_entities.append(entity)

            # Add remaining entities without enhancement
            enhanced_entities.extend(entities[max_entities_to_analyze:])
            logger.info(f"✅ Enhanced {len(enhanced_entities)} entities ({max_entities_to_analyze} analyzed)")
            return enhanced_entities

        except Exception as e:
            logger.warning(f"⚠️ Failed to enhance entities with semantics: {e}")
            return entities

    async def _analyze_entity_with_timeout(self, llm_service, entity_code: str, file_path: str, entity_name: str) -> Dict[str, Any]:
        """Analyze entity with timeout and fallback to prevent infinite loops."""
        try:
            import asyncio

            # Set a strict timeout of 10 seconds per entity
            analysis = await asyncio.wait_for(
                llm_service.analyze_code_semantics(entity_code, file_path, "entity_analysis"),
                timeout=10.0
            )

            # Validate the analysis result
            if not isinstance(analysis, dict):
                logger.warning(f"⚠️ Invalid analysis result for {entity_name}: not a dictionary")
                return self._create_fallback_entity_analysis(entity_name)

            # Check if analysis has required fields
            if 'semantic_tags' not in analysis:
                logger.warning(f"⚠️ Analysis missing semantic_tags for {entity_name}")
                analysis['semantic_tags'] = ['basic_entity']

            return analysis

        except asyncio.TimeoutError:
            logger.warning(f"⏰ Timeout analyzing entity {entity_name} - using fallback")
            return self._create_fallback_entity_analysis(entity_name)
        except Exception as e:
            logger.warning(f"⚠️ Error analyzing entity {entity_name}: {e}")
            return self._create_fallback_entity_analysis(entity_name)

    def _create_fallback_entity_analysis(self, entity_name: str) -> Dict[str, Any]:
        """Create fallback analysis for entities when LLM analysis fails."""
        return {
            "functional_purpose": f"Code entity: {entity_name}",
            "complexity_level": "unknown",
            "key_entities": [entity_name],
            "dependencies": [],
            "patterns": [],
            "test_coverage_areas": [],
            "integration_points": [],
            "semantic_tags": ["fallback_analysis"],
            "improvement_suggestions": [],
            "risk_factors": ["analysis_unavailable"]
        }

    async def _analyze_requirement_semantics(
        self,
        requirement_name: str,
        requirement_description: str,
        test_cases: List[Any],
        page_elements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze requirement semantics using LLM to understand intent and scope."""
        try:
            from app.services.llm_service import llm_service

            # Create comprehensive context
            test_case_summaries = []
            for tc in test_cases[:10]:  # Limit for context
                test_case_summaries.append(f"- {getattr(tc, 'custom_id', 'N/A')}: {tc.title}")

            page_element_summaries = []
            for url, data in list(page_elements.items())[:3]:  # Limit for context
                elements = data.get('elements', [])[:5]  # First 5 elements
                for elem in elements:
                    page_element_summaries.append(f"- {elem.get('tag', 'unknown')}: {elem.get('text', 'no text')[:50]}")

            prompt = f"""Analyze this software requirement for test automation code generation:

REQUIREMENT: {requirement_name}
DESCRIPTION: {requirement_description}

TEST CASES TO IMPLEMENT:
{chr(10).join(test_case_summaries)}

PAGE ELEMENTS AVAILABLE:
{chr(10).join(page_element_summaries)}

Provide a comprehensive analysis in JSON format:
{{
    "functional_area": "string - main functional area (login, registration, etc.)",
    "complexity_level": "string - low/medium/high",
    "ui_components": ["list of UI components involved"],
    "user_workflows": ["list of user workflows to test"],
    "data_requirements": ["list of test data needed"],
    "integration_points": ["list of systems/APIs to integrate with"],
    "risk_factors": ["list of potential implementation risks"],
    "implementation_approach": "string - recommended approach",
    "estimated_effort": "string - effort estimate",
    "summary": "string - concise summary of the requirement"
}}"""

            response = await llm_service._make_request(prompt, "You are an expert test automation architect and business analyst.")

            if response:
                try:
                    # Clean and parse JSON response
                    import json
                    cleaned_response = self._clean_json_response(response)
                    analysis = json.loads(cleaned_response)
                    logger.info(f"📋 Requirement analysis completed for: {requirement_name}")
                    return analysis
                except json.JSONDecodeError as e:
                    logger.warning(f"⚠️ Failed to parse LLM response as JSON: {e}")
                    logger.debug(f"Raw response: {response[:200]}...")
                except Exception as e:
                    logger.warning(f"⚠️ Error processing LLM response: {e}")

            # Fallback analysis
            return {
                "functional_area": requirement_name.lower().split()[0] if requirement_name else "unknown",
                "complexity_level": "medium",
                "ui_components": [],
                "user_workflows": [tc.title for tc in test_cases[:3]],
                "data_requirements": ["test data"],
                "integration_points": [],
                "risk_factors": ["complexity"],
                "implementation_approach": "incremental",
                "estimated_effort": "medium",
                "summary": f"Test automation for {requirement_name}"
            }

        except Exception as e:
            logger.error(f"❌ Failed to analyze requirement semantics: {e}")
            return {"summary": f"Basic analysis for {requirement_name}"}

    async def _generate_implementation_strategy(
        self,
        requirement_analysis: Dict[str, Any],
        code_entities: List[CodeEntity],
        test_mappings: List[TestCaseMapping],
        dependency_graph: Dict[str, List[str]]
    ) -> Dict[str, Any]:
        """Generate intelligent implementation strategy based on analysis."""
        try:
            from app.services.llm_service import llm_service

            # Analyze existing code patterns
            existing_patterns = self._analyze_code_patterns(code_entities)

            # Determine optimal file placement
            target_files = self._determine_optimal_file_placement(
                requirement_analysis, code_entities, existing_patterns
            )

            # Assess implementation complexity
            complexity_score = self._assess_implementation_complexity(
                requirement_analysis, test_mappings, dependency_graph
            )

            # Generate strategy using LLM
            prompt = f"""Generate an implementation strategy for test automation:

REQUIREMENT ANALYSIS:
{json.dumps(requirement_analysis, indent=2)}

EXISTING CODE PATTERNS:
{json.dumps(existing_patterns, indent=2)}

TARGET FILES:
{json.dumps(target_files, indent=2)}

COMPLEXITY SCORE: {complexity_score}

Provide implementation strategy in JSON format:
{{
    "approach": "string - one of: create_new_file, create_new, append_to_existing_file, append_to_most_relevant_file, extend_existing, hybrid, incremental",
    "target_files": ["list of files to create/modify"],
    "dependencies": ["list of dependencies needed"],
    "implementation_order": ["ordered list of implementation steps"],
    "confidence": "float - confidence score 0.0-1.0",
    "reasoning": "string - explanation of strategy choice"
}}

Strategy Options:
- create_new_file/create_new: Create completely new test files
- append_to_existing_file: Add tests to existing file
- append_to_most_relevant_file: Add to most suitable existing file
- extend_existing: Extend existing file with new functionality
- hybrid: Mixed approach - create new files and modify existing ones
- incremental: Intelligent incremental approach based on analysis"""

            response = await llm_service._make_request(prompt, "You are an expert software architect specializing in test automation.")

            if response:
                try:
                    # Clean and parse JSON response
                    cleaned_response = self._clean_json_response(response)
                    strategy = json.loads(cleaned_response)
                    logger.info(f"🎯 Implementation strategy generated with {strategy.get('confidence', 0.8):.2f} confidence")
                    return strategy
                except json.JSONDecodeError as e:
                    logger.warning(f"⚠️ Failed to parse strategy response as JSON: {e}")
                    logger.debug(f"Raw response: {response[:200]}...")
                except Exception as e:
                    logger.warning(f"⚠️ Error processing strategy response: {e}")

            # Fallback strategy - use append_to_existing_file instead of extend_existing
            return {
                "approach": "append_to_existing_file" if code_entities else "create_new",
                "target_files": target_files,
                "dependencies": [],
                "implementation_order": ["setup", "implementation", "validation"],
                "confidence": 0.7,
                "reasoning": "Fallback strategy based on existing code analysis"
            }

        except Exception as e:
            logger.error(f"❌ Failed to generate implementation strategy: {e}")
            return {"approach": "create_new", "confidence": 0.5}

    def _analyze_code_patterns(self, code_entities: List[CodeEntity]) -> Dict[str, Any]:
        """Analyze existing code patterns for intelligent strategy generation."""
        patterns = {
            "test_patterns": [],
            "naming_conventions": [],
            "complexity_distribution": {},
            "file_organization": {},
            "common_dependencies": []
        }

        try:
            if not code_entities:
                return patterns

            # Analyze naming conventions
            test_entities = [e for e in code_entities if 'test' in e.file_path.lower()]
            if test_entities:
                patterns["naming_conventions"] = [
                    "test_" + e.name.lower() for e in test_entities[:5]
                ]

            # Analyze complexity distribution
            complexities = [e.complexity_score for e in code_entities]
            if complexities:
                patterns["complexity_distribution"] = {
                    "avg": sum(complexities) / len(complexities),
                    "max": max(complexities),
                    "min": min(complexities)
                }

            # Analyze file organization
            file_types = {}
            for entity in code_entities:
                file_ext = entity.file_path.split('.')[-1] if '.' in entity.file_path else 'unknown'
                file_types[file_ext] = file_types.get(file_ext, 0) + 1
            patterns["file_organization"] = file_types

            # Common dependencies
            all_deps = []
            for entity in code_entities:
                all_deps.extend(entity.dependencies)

            from collections import Counter
            common_deps = Counter(all_deps).most_common(10)
            patterns["common_dependencies"] = [dep[0] for dep in common_deps]

            logger.info(f"📊 Analyzed code patterns: {len(code_entities)} entities")
            return patterns

        except Exception as e:
            logger.warning(f"⚠️ Failed to analyze code patterns: {e}")
            return patterns

    def _determine_optimal_file_placement(
        self,
        requirement_analysis: Dict[str, Any],
        code_entities: List[CodeEntity],
        existing_patterns: Dict[str, Any]
    ) -> List[str]:
        """Determine optimal file placement based on analysis."""
        target_files = []

        try:
            functional_area = requirement_analysis.get('functional_area', 'unknown')

            # Look for existing files in the same functional area
            relevant_files = set()
            for entity in code_entities:
                if functional_area.lower() in entity.file_path.lower():
                    relevant_files.add(entity.file_path)

            if relevant_files:
                target_files.extend(list(relevant_files)[:3])  # Top 3 relevant files
            else:
                # Suggest new file based on patterns
                file_org = existing_patterns.get('file_organization', {})
                if 'js' in file_org or 'ts' in file_org:
                    target_files.append(f"tests/{functional_area}Test.js")
                elif 'py' in file_org:
                    target_files.append(f"tests/test_{functional_area.lower()}.py")
                elif 'java' in file_org:
                    target_files.append(f"src/test/java/{functional_area}Test.java")
                else:
                    target_files.append(f"tests/{functional_area}Test.js")

            logger.info(f"🎯 Determined optimal file placement: {target_files}")
            return target_files

        except Exception as e:
            logger.warning(f"⚠️ Failed to determine optimal file placement: {e}")
            return [f"tests/{requirement_analysis.get('functional_area', 'unknown')}Test.js"]

    def _assess_implementation_complexity(
        self,
        requirement_analysis: Dict[str, Any],
        test_mappings: List[TestCaseMapping],
        dependency_graph: Dict[str, List[str]]
    ) -> float:
        """Assess implementation complexity score."""
        try:
            complexity_score = 1.0

            # Factor in requirement complexity
            req_complexity = requirement_analysis.get('complexity_level', 'medium')
            if req_complexity == 'high':
                complexity_score += 2.0
            elif req_complexity == 'medium':
                complexity_score += 1.0

            # Factor in test mappings
            if test_mappings:
                avg_coverage = sum(tm.coverage_score for tm in test_mappings) / len(test_mappings)
                complexity_score += (1.0 - avg_coverage) * 2.0

            # Factor in dependency complexity
            if dependency_graph:
                avg_deps = sum(len(deps) for deps in dependency_graph.values()) / len(dependency_graph)
                complexity_score += min(avg_deps / 10.0, 2.0)

            # Factor in integration points
            integration_points = requirement_analysis.get('integration_points', [])
            complexity_score += len(integration_points) * 0.5

            return min(complexity_score, 10.0)  # Cap at 10.0

        except Exception as e:
            logger.warning(f"⚠️ Failed to assess implementation complexity: {e}")
            return 5.0  # Default medium complexity

    async def _create_execution_plan(
        self,
        strategy: Dict[str, Any],
        test_cases: List[Any],
        code_entities: List[CodeEntity],
        page_elements: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Create a detailed execution plan for implementation."""
        try:
            execution_steps = []

            # Step 1: Setup and preparation
            execution_steps.append({
                "step": "Setup test environment",
                "action": "setup_environment",
                "priority": "high",
                "estimated_effort": "low",
                "description": "Prepare test environment and dependencies"
            })

            # Step 2: Create/update test files
            for i, test_case in enumerate(test_cases[:5]):  # Limit to 5 test cases
                test_case_title = getattr(test_case, 'title', f'Test Case {i+1}')
                execution_steps.append({
                    "step": f"Implement test: {test_case_title}",
                    "action": "create_test",
                    "priority": "high",
                    "estimated_effort": "medium",
                    "description": f"Create automated test for {test_case_title}",
                    "test_case_id": str(i)
                })

            # Step 3: Create page objects if needed
            if page_elements and len(page_elements.get('elements', [])) > 0:
                execution_steps.append({
                    "step": "Create page object",
                    "action": "create_page_object",
                    "priority": "medium",
                    "estimated_effort": "medium",
                    "description": "Create page object for UI interactions"
                })

            # Step 4: Integration and validation
            execution_steps.append({
                "step": "Validate implementation",
                "action": "validate_tests",
                "priority": "high",
                "estimated_effort": "low",
                "description": "Validate test implementation and integration"
            })

            return execution_steps

        except Exception as e:
            logger.error(f"❌ Failed to create execution plan: {e}")
            return [
                {
                    "step": "Basic implementation",
                    "action": "create_tests",
                    "priority": "high",
                    "estimated_effort": "medium"
                }
            ]

    async def _assess_implementation_risks(
        self,
        execution_plan: List[Dict[str, Any]],
        code_entities: List[CodeEntity],
        dependency_graph: Dict[str, List[str]]
    ) -> Dict[str, Any]:
        """Assess risks associated with the implementation plan."""
        try:
            risks = {
                "complexity_risks": [],
                "integration_risks": [],
                "maintenance_risks": []
            }

            # Assess complexity risks
            if len(execution_plan) > 10:
                risks["complexity_risks"].append("high_step_count")

            if len(code_entities) > 50:
                risks["complexity_risks"].append("large_codebase")

            # Assess integration risks
            if len(dependency_graph) > 20:
                risks["integration_risks"].append("complex_dependencies")

            # Assess maintenance risks
            if not code_entities:
                risks["maintenance_risks"].append("no_existing_structure")

            # Default to low risk if no specific risks identified
            if not any(risks.values()):
                risks = {
                    "complexity_risks": ["low_complexity"],
                    "integration_risks": ["minimal_integration"],
                    "maintenance_risks": ["standard_maintenance"]
                }

            return risks

        except Exception as e:
            logger.error(f"❌ Failed to assess implementation risks: {e}")
            return {
                "complexity_risks": ["unknown_complexity"],
                "integration_risks": ["unknown_integration"],
                "maintenance_risks": ["unknown_maintenance"]
            }

    def _clean_json_response(self, response: str) -> str:
        """Clean LLM response to ensure valid JSON parsing."""
        if not response:
            return "{}"

        # Remove common markdown formatting
        cleaned = response.strip()

        # Remove markdown code blocks
        if cleaned.startswith('```json'):
            cleaned = cleaned[7:]
        elif cleaned.startswith('```'):
            cleaned = cleaned[3:]

        if cleaned.endswith('```'):
            cleaned = cleaned[:-3]

        # Remove any leading/trailing whitespace
        cleaned = cleaned.strip()

        # If response doesn't start with {, try to find the JSON part
        if not cleaned.startswith('{'):
            # Look for the first { and last }
            start_idx = cleaned.find('{')
            end_idx = cleaned.rfind('}')

            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                cleaned = cleaned[start_idx:end_idx + 1]
            else:
                # If no valid JSON structure found, return empty object
                return "{}"

        # Remove any trailing text after the last }
        last_brace = cleaned.rfind('}')
        if last_brace != -1:
            cleaned = cleaned[:last_brace + 1]

        return cleaned

    def _create_fallback_plan(self, requirement_name: str, test_cases: List[Any]) -> AgenticPlan:
        """Create a fallback plan when agentic planning fails."""
        return AgenticPlan(
            requirement_id=str(hash(requirement_name)),
            requirement_name=requirement_name,
            analysis_summary=f"Basic analysis for {requirement_name}",
            generation_strategy="incremental",
            target_files=[],
            dependencies=[],
            implementation_steps=[
                {
                    "step": "Analyze existing code",
                    "action": "review_codebase",
                    "priority": "high",
                    "estimated_effort": "medium"
                },
                {
                    "step": "Generate test cases",
                    "action": "create_tests",
                    "priority": "high",
                    "estimated_effort": "high"
                }
            ],
            confidence_score=0.6,
            risk_assessment={
                "complexity_risks": ["unknown_complexity"],
                "integration_risks": ["unknown_integration"],
                "maintenance_risks": ["unknown_maintenance"]
            }
        )

    async def _analyze_test_code_mappings(
        self,
        test_cases: List[Any],
        code_entities: List[CodeEntity],
        requirement_name: str
    ) -> List[TestCaseMapping]:
        """Analyze mappings between test cases and code entities."""
        mappings = []

        try:
            for i, test_case in enumerate(test_cases):
                # Create a basic mapping for each test case
                test_case_title = getattr(test_case, 'title', f'Test Case {i+1}')
                test_method_name = f"test_{test_case_title.lower().replace(' ', '_').replace('-', '_')}"

                # Find relevant code entities for this test case
                relevant_entities = []
                test_case_keywords = test_case_title.lower().split()

                for entity in code_entities:
                    # Check if entity is relevant to this test case
                    entity_name_lower = entity.name.lower()
                    entity_file_lower = entity.file_path.lower()

                    relevance_score = 0
                    for keyword in test_case_keywords:
                        if keyword in entity_name_lower or keyword in entity_file_lower:
                            relevance_score += 1

                    if relevance_score > 0:
                        relevant_entities.append(entity)

                # Create mapping
                mapping = TestCaseMapping(
                    test_case_id=str(i),
                    test_method_name=test_method_name,
                    target_entities=relevant_entities[:5],  # Limit to top 5 relevant entities
                    coverage_score=min(len(relevant_entities) / 3.0, 1.0),  # Normalize to 0-1
                    semantic_similarity=0.7,  # Default similarity score
                    implementation_status='missing'
                )
                mappings.append(mapping)

            logger.info(f"✅ Created {len(mappings)} test case mappings")
            return mappings

        except Exception as e:
            logger.error(f"❌ Failed to analyze test code mappings: {e}")
            return []

    async def _build_dependency_graph(self, code_entities: List[CodeEntity]) -> Dict[str, List[str]]:
        """Build a dependency graph from code entities."""
        if not code_entities:
            return {}

        dependency_graph = {}

        try:
            # Simple dependency analysis based on imports and function calls
            for entity in code_entities:
                entity_key = f"{entity.file_path}:{entity.name}"
                dependency_graph[entity_key] = entity.dependencies

            logger.info(f"📊 Built dependency graph with {len(dependency_graph)} entities")
            return dependency_graph

        except Exception as e:
            logger.error(f"❌ Failed to build dependency graph: {e}")
            return {}

    async def get_detailed_code_context_for_incremental_generation(
        self,
        project_id: int,
        project_name: str,
        requirement_id: int,
        requirement_name: str,
        new_test_cases: List[Any],
        language: ProgrammingLanguage
    ) -> Dict[str, Any]:
        """
        Get detailed code context for incremental code generation.
        Provides comprehensive information about existing code structure,
        test cases, and where new test cases should be added.

        Args:
            project_id: Unique ID of the project
            project_name: Name of the project (for git operations)
            requirement_id: Unique ID of the requirement
            requirement_name: Name of the requirement
            new_test_cases: List of new test cases to be added
            language: Programming language

        Returns:
            Dictionary with detailed context information
        """
        from app.services.git_service import git_service

        try:
            # Get all repository files
            repo_files = git_service.get_repository_files(project_name)

            # Analyze existing test files and structure
            test_files = []
            page_object_files = []
            config_files = []
            existing_test_cases = []

            logger.info(f"Starting file analysis for project: {project_name}, found {len(repo_files)} files")

            for file_info in repo_files:
                if not file_info['is_text']:
                    continue

                file_path = file_info['path']
                file_content = git_service.read_file_content(project_name, file_path)

                if not file_content:
                    continue

                # Categorize files
                if self._is_test_file(file_path, language):
                    test_files.append({
                        'path': file_path,
                        'content': file_content,
                        'size': len(file_content),
                        'test_methods': self._extract_test_methods(file_content, language)
                    })

                    # Extract existing test cases from this file
                    file_test_cases = self._extract_test_cases_from_file(file_content, file_path, language)
                    existing_test_cases.extend(file_test_cases)

                    # Enhanced logging with test case details
                    if file_test_cases:
                        test_case_ids = [tc.get('test_case_id', 'NO-ID') for tc in file_test_cases]
                        logger.info(f"📋 Extracted {len(file_test_cases)} test cases from {file_path}: {test_case_ids}")
                    else:
                        logger.info(f"📋 No test cases found in {file_path}")

                elif self._is_page_object_file(file_path, language):
                    page_object_files.append({
                        'path': file_path,
                        'content': file_content[:2000],  # Limit content
                        'classes': self._extract_class_names(file_content, language)
                    })

                elif self._is_config_file(file_path, language):
                    config_files.append({
                        'path': file_path,
                        'content': file_content[:1000]  # Limit content
                    })

            # Find the most relevant test file for the requirement first
            target_test_file = await self._find_target_test_file(
                test_files, requirement_name, new_test_cases, project_id
            )

            # Determine which test cases actually need to be implemented (requirement-aware)
            unimplemented_test_cases = await self.determine_unimplemented_test_cases(
                new_test_cases, existing_test_cases, requirement_name, target_test_file
            )

            # Filter existing test cases to only show relevant ones for this requirement
            if target_test_file and target_test_file.get('action') == 'create_new':
                # When creating new file, no existing test cases are relevant
                relevant_existing_test_cases = []
                logger.info(f"Creating new file - no relevant existing test cases for requirement '{requirement_name}'")
            elif requirement_name and target_test_file:
                # When appending to existing file, filter to relevant test cases
                relevant_existing_test_cases = await self._filter_relevant_existing_test_cases(
                    existing_test_cases, requirement_name, target_test_file
                )
                logger.info(f"Filtered to {len(relevant_existing_test_cases)} relevant existing test cases for requirement '{requirement_name}'")
            else:
                relevant_existing_test_cases = existing_test_cases
                logger.info(f"Using all {len(existing_test_cases)} existing test cases (no requirement filtering)")

            # Get the highest test case number from existing methods to determine next method number
            highest_test_number = 0
            for existing_tc in relevant_existing_test_cases:
                method_name = existing_tc.get('method_name', '')
                test_number = self.get_next_test_case_number_from_method_name(method_name)
                highest_test_number = max(highest_test_number, test_number)

            # Log final counts before generating context
            logger.info(f"Final analysis results for {project_name}:")
            logger.info(f"  - Test files: {len(test_files)}")
            logger.info(f"  - Page object files: {len(page_object_files)}")
            logger.info(f"  - Config files: {len(config_files)}")
            logger.info(f"  - Relevant existing test cases: {len(relevant_existing_test_cases)}")
            logger.info(f"  - Unimplemented test cases: {len(unimplemented_test_cases)}")

            # Generate context
            context = {
                'project_structure': {
                    'test_files_count': len(test_files),
                    'page_object_files_count': len(page_object_files),
                    'config_files_count': len(config_files)
                },
                'existing_test_cases': relevant_existing_test_cases,
                'unimplemented_test_cases': [
                    {
                        'custom_id': getattr(tc, 'custom_id', ''),
                        'title': tc.title,
                        'steps': tc.steps,
                        'expected_result': tc.expected_result
                    } for tc in unimplemented_test_cases
                ],
                'target_test_file': target_test_file,
                'test_files': test_files[:3],  # Limit to most relevant files
                'page_object_files': page_object_files[:2],
                'config_files': config_files[:2],
                'all_test_cases': [
                    {
                        'custom_id': getattr(tc, 'custom_id', ''),
                        'title': tc.title,
                        'steps': tc.steps,
                        'expected_result': tc.expected_result
                    } for tc in new_test_cases
                ],
                'test_cases_to_implement': [
                    {
                        'custom_id': getattr(tc, 'custom_id', ''),
                        'title': tc.title,
                        'steps': tc.steps,
                        'expected_result': tc.expected_result
                    } for tc in unimplemented_test_cases
                ],
                'next_method_number': highest_test_number + 1,
                'generation_strategy': await self._determine_generation_strategy(
                    test_files, requirement_name, unimplemented_test_cases, project_id
                ),
                'implementation_summary': {
                    'total_test_cases': len(new_test_cases),
                    'implemented_count': len(new_test_cases) - len(unimplemented_test_cases),  # Only count relevant implemented cases
                    'unimplemented_count': len(unimplemented_test_cases),
                    'next_to_implement': [getattr(tc, 'custom_id', '') for tc in unimplemented_test_cases[:5]]
                }
            }

            return context

        except Exception as e:
            logger.error(f"Error getting detailed code context: {e}")
            return {
                'error': str(e),
                'fallback_context': self.get_code_context_for_generation(
                    project_name, requirement_name, language
                )
            }
    
    def delete_project_embeddings(self, project_id: int) -> bool:
        """
        Delete all embeddings for a specific project.

        Args:
            project_id: Unique ID of the project

        Returns:
            True if successful, False otherwise
        """
        try:
            # Search for points with this project_id
            search_result = self.client.scroll(
                collection_name=self.collection_name,
                scroll_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="project_id",
                            match=models.MatchValue(value=project_id)
                        )
                    ]
                ),
                limit=1000  # Process in batches
            )

            # Delete found points
            point_ids = [point.id for point in search_result[0]]
            if point_ids:
                self.client.delete(
                    collection_name=self.collection_name,
                    points_selector=models.PointIdsList(points=point_ids)
                )
                logger.info(f"Deleted {len(point_ids)} code embeddings for project ID {project_id}")

            return True

        except Exception as e:
            logger.error(f"Error deleting project embeddings: {e}")
            return False
    
    def health_check(self) -> bool:
        """Check if the code embedding service is healthy."""
        try:
            collections = self.client.get_collections()
            return self.collection_name in [col.name for col in collections.collections]
        except Exception as e:
            logger.error(f"Code embedding service health check failed: {e}")
            return False

    def _is_test_file(self, file_path: str, language: ProgrammingLanguage) -> bool:
        """Check if a file is a test file based on naming conventions."""
        file_path_lower = file_path.lower()

        if language == ProgrammingLanguage.PYTHON:
            return (
                'test_' in file_path_lower or
                '_test.py' in file_path_lower or
                'tests/' in file_path_lower
            )
        elif language == ProgrammingLanguage.JAVASCRIPT:
            return (
                '.test.' in file_path_lower or
                '.spec.' in file_path_lower or
                'test/' in file_path_lower or
                'tests/' in file_path_lower
            )
        elif language == ProgrammingLanguage.JAVA:
            return (
                'test' in file_path_lower and
                file_path_lower.endswith('.java')
            )

        return False

    def _is_page_object_file(self, file_path: str, language: ProgrammingLanguage) -> bool:
        """Check if a file is a page object file."""
        file_path_lower = file_path.lower()

        return (
            'page' in file_path_lower or
            'pages/' in file_path_lower or
            'pageobject' in file_path_lower or
            'page_object' in file_path_lower
        )

    def _is_config_file(self, file_path: str, language: ProgrammingLanguage) -> bool:
        """Check if a file is a configuration file."""
        file_path_lower = file_path.lower()
        config_files = [
            'config', 'settings', 'setup', 'conftest',
            'requirements.txt', 'package.json', 'pom.xml'
        ]

        return any(config_name in file_path_lower for config_name in config_files)

    def _extract_test_methods(self, file_content: str, language: ProgrammingLanguage) -> List[str]:
        """Extract test method names from file content with enhanced pattern matching."""
        import re

        test_methods = []

        if language == ProgrammingLanguage.PYTHON:
            # Enhanced Python test method patterns
            patterns = [
                r'def\s+(test_\w+)\s*\(',           # def test_method()
                r'def\s+(test\d+\w*)\s*\(',         # def test26_method()
                r'async\s+def\s+(test_\w+)\s*\(',   # async def test_method()
                r'async\s+def\s+(test\d+\w*)\s*\(', # async def test26_method()
            ]
            for pattern in patterns:
                matches = re.findall(pattern, file_content, re.IGNORECASE)
                test_methods.extend(matches)

        elif language == ProgrammingLanguage.JAVASCRIPT:
            # Enhanced JavaScript test method patterns
            patterns = [
                # Traditional test function calls
                r'it\s*\(\s*[\'"`]([^\'"`]+)[\'"`]',
                r'test\s*\(\s*[\'"`]([^\'"`]+)[\'"`]',
                r'describe\s*\(\s*[\'"`]([^\'"`]+)[\'"`]',

                # Class method definitions with various formats
                r'(?:async\s+)?(test\w*\d+\w*)\s*\(',     # test26_method, testTC001
                r'(?:async\s+)?(tc\d+\w*)\s*\(',          # tc26_method
                r'(?:async\s+)?(\d+_test\w*)\s*\(',       # 26_test_method

                # Function definitions
                r'function\s+(test\w*\d*\w*)\s*\(',       # function test26_method()
                r'const\s+(test\w*\d*\w*)\s*=',           # const test26_method =
                r'let\s+(test\w*\d*\w*)\s*=',             # let test26_method =
                r'var\s+(test\w*\d*\w*)\s*=',             # var test26_method =

                # Arrow functions
                r'(test\w*\d*\w*)\s*=\s*(?:async\s*)?\(',  # test26_method = async (
            ]
            for pattern in patterns:
                matches = re.findall(pattern, file_content, re.IGNORECASE)
                test_methods.extend(matches)

        elif language == ProgrammingLanguage.JAVA:
            # Enhanced Java test method patterns
            patterns = [
                r'@Test[^}]*?public\s+void\s+(\w+)\s*\(',     # @Test public void method()
                r'@Test[^}]*?void\s+(\w+)\s*\(',              # @Test void method()
                r'@ParameterizedTest[^}]*?void\s+(\w+)\s*\(', # @ParameterizedTest void method()
            ]
            for pattern in patterns:
                matches = re.findall(pattern, file_content, re.DOTALL | re.IGNORECASE)
                test_methods.extend(matches)

        # Remove duplicates while preserving order
        seen = set()
        unique_methods = []
        for method in test_methods:
            if method not in seen:
                seen.add(method)
                unique_methods.append(method)

        logger.debug(f"Extracted {len(unique_methods)} unique test methods: {unique_methods}")
        return unique_methods

    def _extract_class_names(self, file_content: str, language: ProgrammingLanguage) -> List[str]:
        """Extract class names from file content."""
        import re

        class_names = []

        if language == ProgrammingLanguage.PYTHON:
            pattern = r'class\s+(\w+)\s*[\(:]'
            matches = re.findall(pattern, file_content)
            class_names.extend(matches)

        elif language in (ProgrammingLanguage.JAVASCRIPT, ProgrammingLanguage.JAVA):
            pattern = r'class\s+(\w+)\s*[{]'
            matches = re.findall(pattern, file_content)
            class_names.extend(matches)

        return class_names

    def _extract_test_cases_from_file(self, file_content: str, file_path: str, language: ProgrammingLanguage) -> List[Dict[str, Any]]:
        """Extract existing test cases from a test file."""
        import re

        test_cases = []
        test_methods = self._extract_test_methods(file_content, language)

        logger.debug(f"Extracting test cases from {len(test_methods)} test methods in file: {file_path}")

        for method_name in test_methods:
            # Extract test case ID from method name (e.g., test26_LoginWithEmptyPassword -> TC-026)
            test_case_id = self._extract_test_case_id_from_method_name(method_name)

            # Try to extract test case information from method name and docstring
            test_case = {
                'method_name': method_name,
                'file_path': file_path,
                'title': self._method_name_to_title(method_name),
                'estimated_steps': self._extract_method_steps(file_content, method_name, language),
                'test_case_id': test_case_id,
                'implemented': True
            }
            test_cases.append(test_case)
            logger.debug(f"Extracted test case: {method_name} -> {test_case_id}")

        logger.info(f"Extracted {len(test_cases)} test cases from file: {file_path}")
        return test_cases

    def _extract_test_case_id_from_method_name(self, method_name: str) -> str:
        """Extract test case ID from method name with comprehensive pattern matching."""
        import re

        # Comprehensive patterns for test case ID extraction
        patterns = [
            # Standard patterns with TC prefix
            r'(?:async\s+)?test[_]?TC[_-]?(\d+)',  # testTC001, test_TC_001, testTC-001
            r'(?:async\s+)?tc[_-]?(\d+)',          # tc001, tc_001, tc-001

            # Numbered test patterns
            r'(?:async\s+)?test(\d+)[_-]',         # test26_, test26-
            r'(?:async\s+)?test[_-](\d+)[_-]',     # test_26_, test-26-
            r'(?:async\s+)?testCase(\d+)',         # testCase26
            r'(?:async\s+)?test_case_(\d+)',       # test_case_26

            # Method name with numbers at start
            r'(?:async\s+)?(\d+)[_-]test',         # 26_test, 26-test

            # Numbers in method name (more flexible)
            r'(?:async\s+)?test.*?(\d+)',          # test...26... (any number in test method)
        ]

        for pattern in patterns:
            match = re.search(pattern, method_name, re.IGNORECASE)
            if match:
                number = int(match.group(1))
                extracted_id = f"TC-{number:03d}"
                logger.debug(f"Extracted test case ID '{extracted_id}' from method name '{method_name}' using pattern '{pattern}'")
                return extracted_id

        # Enhanced fallback: look for any sequence of digits
        number_matches = re.findall(r'(\d+)', method_name)
        if number_matches:
            # Use the first number found, or the largest if multiple
            numbers = [int(n) for n in number_matches]
            # Prefer numbers that look like test case IDs (1-999)
            valid_numbers = [n for n in numbers if 1 <= n <= 999]
            if valid_numbers:
                number = valid_numbers[0]  # Use first valid number
            else:
                number = numbers[0]  # Use first number found

            extracted_id = f"TC-{number:03d}"
            logger.debug(f"Extracted test case ID '{extracted_id}' from method name '{method_name}' using enhanced fallback")
            return extracted_id

        logger.debug(f"Could not extract test case ID from method name '{method_name}', returning TC-UNKNOWN")
        return "TC-UNKNOWN"

    async def determine_unimplemented_test_cases(
        self,
        all_test_cases: List[Any],
        existing_test_cases: List[Dict[str, Any]],
        requirement_name: str = None,
        target_test_file: Dict[str, Any] = None
    ) -> List[Any]:
        """Determine which test cases need to be implemented with enhanced accuracy."""

        # Enhanced logging for debugging
        logger.info(f"🔍 Determining unimplemented test cases for requirement: '{requirement_name}'")
        logger.info(f"📊 Input: {len(all_test_cases)} new test cases, {len(existing_test_cases)} existing test cases")

        # If we're creating a new file, don't use existing test cases from other files
        if target_test_file and target_test_file.get('action') == 'create_new':
            relevant_existing_cases = []
            logger.info(f"🆕 Creating new file - no existing test cases to consider")
        elif requirement_name and target_test_file:
            # Enhanced filtering with better semantic understanding
            relevant_existing_cases = await self._filter_relevant_existing_test_cases(
                existing_test_cases, requirement_name, target_test_file
            )
            logger.info(f"🎯 Filtered to {len(relevant_existing_cases)} relevant existing test cases")
        else:
            relevant_existing_cases = existing_test_cases
            logger.info(f"📋 Using all {len(existing_test_cases)} existing test cases (no requirement filtering)")

        # Enhanced ID extraction and matching
        implemented_ids = set()
        implemented_titles = set()

        for existing_tc in relevant_existing_cases:
            tc_id = existing_tc.get('test_case_id')
            tc_title = existing_tc.get('title', '').lower().strip()
            method_name = existing_tc.get('method_name', '')

            # Add ID if valid
            if tc_id and tc_id != 'TC-UNKNOWN':
                implemented_ids.add(tc_id)
                logger.debug(f"✅ Found implemented test case ID: {tc_id} ({method_name})")

            # Add normalized title for fuzzy matching
            if tc_title and len(tc_title) > 3:
                implemented_titles.add(tc_title)
                logger.debug(f"📝 Found implemented test case title: '{tc_title}'")

        # Enhanced unimplemented detection
        unimplemented = []
        for test_case in all_test_cases:
            tc_id = getattr(test_case, 'custom_id', None)
            tc_title = getattr(test_case, 'title', '').lower().strip()

            is_implemented = False

            # Check by ID (primary method)
            if tc_id and tc_id in implemented_ids:
                is_implemented = True
                logger.debug(f"✅ Test case {tc_id} already implemented (ID match)")

            # Check by title similarity (secondary method)
            elif tc_title and len(tc_title) > 3:
                # Exact title match
                if tc_title in implemented_titles:
                    is_implemented = True
                    logger.debug(f"✅ Test case {tc_id} already implemented (exact title match)")
                else:
                    # Fuzzy title matching for similar test cases
                    for impl_title in implemented_titles:
                        similarity = self._calculate_title_similarity(tc_title, impl_title)
                        if similarity > 0.8:  # High similarity threshold
                            is_implemented = True
                            logger.debug(f"✅ Test case {tc_id} already implemented (fuzzy title match: {similarity:.2f})")
                            break

            if not is_implemented:
                unimplemented.append(test_case)
                logger.debug(f"❌ Test case {tc_id} ({tc_title}) is unimplemented")

        logger.info(f"📈 Final result: {len(unimplemented)} unimplemented out of {len(all_test_cases)} total")

        # Log the specific unimplemented test cases
        if unimplemented:
            unimpl_ids = [getattr(tc, 'custom_id', 'NO-ID') for tc in unimplemented]
            logger.info(f"🔄 Unimplemented test case IDs: {unimpl_ids}")

        # Sort by custom_id to ensure sequential implementation
        unimplemented.sort(key=lambda tc: getattr(tc, 'custom_id', 'TC-999'))

        return unimplemented

    async def _filter_relevant_existing_test_cases(
        self,
        existing_test_cases: List[Dict[str, Any]],
        requirement_name: str,
        target_test_file: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Filter existing test cases to only those relevant to the current requirement."""

        # If we have high confidence in the target file match, use all test cases from that file
        if target_test_file.get('confidence', 0) > 0.7:
            target_file_path = target_test_file.get('file_path')
            if target_file_path:
                relevant_cases = [
                    tc for tc in existing_test_cases
                    if tc.get('file_path') == target_file_path
                ]
                logger.info(f"High confidence match: using all {len(relevant_cases)} test cases from {target_file_path}")
                return relevant_cases

        # For lower confidence matches, use semantic filtering
        try:
            relevant_cases = await self._semantic_filter_test_cases(existing_test_cases, requirement_name)
            logger.info(f"Semantic filtering: found {len(relevant_cases)} relevant test cases")
            return relevant_cases
        except Exception as e:
            logger.warning(f"Semantic filtering failed: {e}, using all existing test cases")
            return existing_test_cases

    async def _semantic_filter_test_cases(
        self,
        existing_test_cases: List[Dict[str, Any]],
        requirement_name: str
    ) -> List[Dict[str, Any]]:
        """Use enhanced semantic analysis to filter test cases relevant to the requirement."""

        if not existing_test_cases:
            return existing_test_cases

        # First, try keyword-based filtering for better accuracy
        keyword_filtered = self._keyword_filter_test_cases(existing_test_cases, requirement_name)

        # If keyword filtering gives good results, use it
        if len(keyword_filtered) > 0 and len(keyword_filtered) < len(existing_test_cases):
            logger.info(f"Keyword filtering found {len(keyword_filtered)} relevant test cases")
            return keyword_filtered

        # Fall back to LLM-based filtering if available
        if not self.is_available:
            logger.info("Vector service unavailable, using keyword filtering results")
            return keyword_filtered

        try:
            from app.services.llm_service import llm_service
            import asyncio

            # Create a more detailed summary of existing test cases
            test_case_summary = []
            for tc in existing_test_cases[:15]:  # Limit to 15 for better LLM analysis
                method_name = tc.get('method_name', 'unknown')
                title = tc.get('title', 'unknown')
                file_path = tc.get('file_path', 'unknown')
                test_case_id = tc.get('test_case_id', 'unknown')

                # Include more context for better analysis
                test_case_summary.append(f"- {method_name} | ID: {test_case_id} | Title: {title} | File: {file_path}")

            prompt = f"""You are analyzing test automation code to determine which existing test cases are functionally related to a new requirement.

REQUIREMENT TO IMPLEMENT: "{requirement_name}"

EXISTING TEST CASES IN CODEBASE:
{chr(10).join(test_case_summary)}

TASK: Identify which existing test cases are functionally related to the requirement "{requirement_name}".

CRITERIA FOR RELATEDNESS:
1. Same functional area (e.g., login, registration, checkout)
2. Same user workflow or feature
3. Would logically belong in the same test suite
4. Test similar UI components or backend services

IMPORTANT: Be conservative - only include test cases that are clearly related to avoid cross-functional contamination.

RESPONSE FORMAT: List only the method names of related test cases, one per line. If none are related, respond with "NONE".

EXAMPLE:
test1_LoginWithValidCredentials
test2_LoginWithInvalidPassword
"""

            # Get LLM response with timeout
            response = await asyncio.wait_for(
                llm_service._make_request(prompt, "You are an expert test automation architect with 10+ years of experience."),
                timeout=15.0
            )

            if response and response.strip().upper() != "NONE":
                # Extract method names from response with improved parsing
                related_method_names = set()
                for line in response.strip().split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#') and not line.startswith('//') and not line.startswith('NONE'):
                        # Extract method name with multiple patterns
                        import re
                        patterns = [
                            r'^([a-zA-Z_]\w*test\w*)',  # Method names starting with letters
                            r'^(test\w+)',              # Method names starting with 'test'
                            r'(test\w+)',               # Any 'test' method in the line
                        ]

                        for pattern in patterns:
                            method_match = re.search(pattern, line, re.IGNORECASE)
                            if method_match:
                                method_name = method_match.group(1)
                                # Validate it's actually in our test cases
                                if any(tc.get('method_name') == method_name for tc in existing_test_cases):
                                    related_method_names.add(method_name)
                                break

                # Filter test cases based on LLM response
                relevant_cases = [
                    tc for tc in existing_test_cases
                    if tc.get('method_name') in related_method_names
                ]

                logger.info(f"LLM identified {len(relevant_cases)} related test cases: {related_method_names}")
                return relevant_cases

            # If LLM says none are related, return keyword filtered results as fallback
            logger.info("LLM determined no existing test cases are related, using keyword filtering")
            return keyword_filtered

        except asyncio.TimeoutError:
            logger.warning("LLM semantic filtering timed out, using keyword filtering")
            return keyword_filtered
        except Exception as e:
            logger.warning(f"Semantic test case filtering failed: {e}, using keyword filtering")
            return keyword_filtered

    def _keyword_filter_test_cases(
        self,
        existing_test_cases: List[Dict[str, Any]],
        requirement_name: str
    ) -> List[Dict[str, Any]]:
        """Filter test cases using keyword matching for better accuracy."""

        if not requirement_name or not existing_test_cases:
            return existing_test_cases

        # Extract keywords from requirement name
        requirement_keywords = set()
        for word in requirement_name.lower().split():
            if len(word) > 2:  # Skip very short words
                requirement_keywords.add(word)
                # Add partial matches for compound words
                if len(word) > 4:
                    requirement_keywords.add(word[:4])

        relevant_cases = []

        for tc in existing_test_cases:
            method_name = tc.get('method_name', '').lower()
            title = tc.get('title', '').lower()
            file_path = tc.get('file_path', '').lower()

            # Combine all text for analysis
            combined_text = f"{method_name} {title} {file_path}"

            # Calculate relevance score
            relevance_score = 0
            for keyword in requirement_keywords:
                if keyword in combined_text:
                    relevance_score += 1
                    # Bonus for exact matches in method name
                    if keyword in method_name:
                        relevance_score += 1

            # Include test cases with reasonable relevance
            if relevance_score > 0:
                relevant_cases.append(tc)
                logger.debug(f"Keyword match: {tc.get('method_name')} (score: {relevance_score})")

        logger.info(f"Keyword filtering found {len(relevant_cases)} relevant test cases out of {len(existing_test_cases)}")
        return relevant_cases

    def _calculate_title_similarity(self, title1: str, title2: str) -> float:
        """Calculate similarity between two test case titles using multiple methods."""
        if not title1 or not title2:
            return 0.0

        # Normalize titles
        t1 = title1.lower().strip()
        t2 = title2.lower().strip()

        # Exact match
        if t1 == t2:
            return 1.0

        # Jaccard similarity (word-based)
        words1 = set(t1.split())
        words2 = set(t2.split())

        if not words1 or not words2:
            return 0.0

        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        jaccard_sim = intersection / union if union > 0 else 0.0

        # Character-based similarity (for typos and variations)
        char_sim = self._calculate_character_similarity(t1, t2)

        # Combined similarity (weighted average)
        combined_sim = (jaccard_sim * 0.7) + (char_sim * 0.3)

        return combined_sim

    def _calculate_character_similarity(self, s1: str, s2: str) -> float:
        """Calculate character-level similarity using Levenshtein-like approach."""
        if not s1 or not s2:
            return 0.0

        # Simple character overlap ratio
        chars1 = set(s1)
        chars2 = set(s2)

        if not chars1 or not chars2:
            return 0.0

        intersection = len(chars1.intersection(chars2))
        union = len(chars1.union(chars2))

        return intersection / union if union > 0 else 0.0

    def get_next_test_case_number_from_method_name(self, method_name: str) -> int:
        """Extract the next test case number that should be used for method naming."""
        import re

        # Extract number from method name
        patterns = [
            r'(?:async\s+)?test(\d+)_',
            r'(?:async\s+)?test_(\d+)_',
            r'(?:async\s+)?testCase(\d+)',
            r'(?:async\s+)?tc(\d+)',
        ]

        for pattern in patterns:
            match = re.search(pattern, method_name, re.IGNORECASE)
            if match:
                return int(match.group(1))

        # If no pattern matches, try to extract any number
        number_match = re.search(r'(\d+)', method_name)
        if number_match:
            return int(number_match.group(1))

        return 1

    def _method_name_to_title(self, method_name: str) -> str:
        """Convert method name to readable title with enhanced parsing."""
        if not method_name:
            return "Unknown Test"

        import re
        title = method_name

        # Remove common prefixes with more specific patterns
        prefixes_patterns = [
            r'^async\s*_?\s*test_?',  # async test, async_test, async_test_
            r'^test_?',               # test, test_
            r'^tc_?',                 # tc, tc_
        ]

        for pattern in prefixes_patterns:
            title = re.sub(pattern, '', title, flags=re.IGNORECASE)

        # Remove test case numbers and IDs more intelligently
        # Pattern: TC001_, 26_, case_26_, etc.
        title = re.sub(r'^(tc|case)?_?\d+_?', '', title, flags=re.IGNORECASE)

        # Remove leading numbers
        title = re.sub(r'^\d+[_-]?', '', title)

        # Convert camelCase to spaced format
        # Handle sequences like "LoginWithValidCredentials" -> "Login With Valid Credentials"
        title = re.sub(r'([a-z])([A-Z])', r'\1 \2', title)

        # Handle snake_case and kebab-case
        title = title.replace('_', ' ').replace('-', ' ')

        # Clean up multiple spaces and title case
        words = [word.capitalize() for word in title.split() if word and word.lower() not in ['test', 'case']]
        title = ' '.join(words)

        return title if title else "Unknown Test"

    def _extract_method_steps(self, file_content: str, method_name: str, language: ProgrammingLanguage) -> List[str]:
        """Extract steps from a test method (simplified)."""
        import re

        # Find the method in the content
        if language == ProgrammingLanguage.PYTHON:
            pattern = rf'def\s+{re.escape(method_name)}\s*\([^)]*\):(.*?)(?=def\s+|\Z)'
            match = re.search(pattern, file_content, re.DOTALL)
            if match:
                method_body = match.group(1)
                # Extract comments and assertions as steps
                steps = re.findall(r'#\s*(.+)', method_body)
                assertions = re.findall(r'assert\s+(.+)', method_body)
                return steps + [f"Assert: {assertion}" for assertion in assertions[:3]]

        return []

    async def _find_semantically_matching_test_file(self, test_files: List[Dict], requirement_name: str, new_test_cases: List[Any], project_requirements: List[str] = None) -> Dict[str, Any]:
        """Find the most semantically relevant test file using embeddings and LLM analysis."""
        if not self.is_available or not test_files:
            return None

        try:
            # Create requirement context from name and test cases
            requirement_context = self._create_requirement_context(requirement_name, new_test_cases)

            best_match = None
            best_confidence = 0.0

            for test_file in test_files:
                # Create test file context from its content and test methods
                test_file_context = self._create_test_file_context(test_file)

                # Calculate semantic similarity using embeddings
                similarity_score = self._calculate_semantic_similarity(
                    requirement_context, test_file_context
                )

                # Use LLM to validate the match and get confidence
                confidence = await self._validate_requirement_test_match(
                    requirement_name, new_test_cases, test_file, similarity_score
                )

                logger.info(f"Semantic analysis for {test_file['path']}: similarity={similarity_score:.3f}, confidence={confidence:.3f}")

                if confidence > best_confidence and confidence > 0.7:  # High confidence threshold
                    best_confidence = confidence
                    best_match = {**test_file, 'confidence': confidence}

            return best_match

        except Exception as e:
            logger.warning(f"Semantic matching failed: {e}, falling back to keyword matching")
            return None

    def _create_requirement_context(self, requirement_name: str, test_cases: List[Any]) -> str:
        """Create a context string from requirement name and test cases."""
        context_parts = [f"Requirement: {requirement_name}"]

        if test_cases:
            context_parts.append("Test scenarios:")
            for i, tc in enumerate(test_cases[:5], 1):  # Limit to first 5 for context
                title = getattr(tc, 'title', 'Unknown')
                steps = getattr(tc, 'steps', [])
                context_parts.append(f"{i}. {title}")
                if steps:
                    context_parts.append(f"   Steps: {' -> '.join(steps[:3])}")  # First 3 steps

        return "\n".join(context_parts)

    def _create_test_file_context(self, test_file: Dict) -> str:
        """Create a context string from test file content and methods."""
        context_parts = [f"Test file: {test_file['path']}"]

        # Add test method names for context
        test_methods = test_file.get('test_methods', [])
        if test_methods:
            context_parts.append("Test methods:")
            for method in test_methods[:10]:  # Limit to first 10 methods
                context_parts.append(f"- {method}")

        # Add a snippet of file content for additional context
        content = test_file.get('content', '')
        if content:
            # Extract comments and key parts for context
            lines = content.split('\n')[:50]  # First 50 lines
            relevant_lines = []
            for line in lines:
                line = line.strip()
                if (line.startswith('//') or line.startswith('#') or
                    'describe(' in line or 'it(' in line or 'test(' in line):
                    relevant_lines.append(line)

            if relevant_lines:
                context_parts.append("Key content:")
                context_parts.extend(relevant_lines[:10])  # Limit to 10 relevant lines

        return "\n".join(context_parts)

    def _calculate_semantic_similarity(self, requirement_context: str, test_file_context: str) -> float:
        """Calculate semantic similarity between requirement and test file using embeddings."""
        try:
            # Generate embeddings for both contexts
            req_embedding = self.encoder.encode(requirement_context)
            file_embedding = self.encoder.encode(test_file_context)

            # Calculate cosine similarity
            from numpy import dot
            from numpy.linalg import norm

            similarity = dot(req_embedding, file_embedding) / (norm(req_embedding) * norm(file_embedding))
            return float(similarity)

        except Exception as e:
            logger.warning(f"Embedding similarity calculation failed: {e}")
            return 0.0

    async def _validate_requirement_test_match(self, requirement_name: str, test_cases: List[Any], test_file: Dict, similarity_score: float) -> float:
        """Use LLM to validate if the test file is appropriate for the requirement."""
        try:
            from app.services.llm_service import llm_service

            # Create validation prompt
            test_case_titles = [getattr(tc, 'title', 'Unknown') for tc in test_cases[:5]]
            test_methods = test_file.get('test_methods', [])[:10]

            prompt = f"""Analyze if this existing test file is appropriate for implementing new test cases for a specific requirement.

REQUIREMENT: {requirement_name}

NEW TEST CASES TO IMPLEMENT:
{chr(10).join(f"- {title}" for title in test_case_titles)}

EXISTING TEST FILE: {test_file['path']}
EXISTING TEST METHODS:
{chr(10).join(f"- {method}" for method in test_methods)}

SEMANTIC SIMILARITY SCORE: {similarity_score:.3f}

Question: Should the new test cases for "{requirement_name}" be added to this existing test file?

Consider:
1. Do the existing test methods cover the same functional area as the new requirement?
2. Would adding the new test cases to this file make logical sense?
3. Are the test scenarios related to the same feature/module?

Respond with a confidence score from 0.0 to 1.0:
- 1.0 = Perfect match, definitely add to this file
- 0.8-0.9 = Very good match, likely appropriate
- 0.6-0.7 = Moderate match, could work but not ideal
- 0.3-0.5 = Poor match, probably should create new file
- 0.0-0.2 = No match, definitely create new file

Respond with only the confidence score (e.g., "0.85")."""

            # Get LLM response
            response = await llm_service._make_request(prompt, "You are an expert test automation architect.")

            if response:
                # Extract confidence score from response
                import re
                score_match = re.search(r'(\d+\.?\d*)', response.strip())
                if score_match:
                    confidence = float(score_match.group(1))
                    return min(max(confidence, 0.0), 1.0)  # Clamp between 0 and 1

            return 0.0

        except Exception as e:
            logger.warning(f"LLM validation failed: {e}")
            # Fall back to similarity score as confidence
            return max(0.0, min(similarity_score, 1.0))

    def _get_project_requirements_context(self, project_id: int) -> List[str]:
        """Get all requirement names from the project for better semantic context."""
        try:
            from app.crud.requirement import get_requirements_by_project_id
            from app.db.session import SessionLocal

            db = SessionLocal()
            try:
                requirements = get_requirements_by_project_id(db, project_id)
                requirement_names = [req.name for req in requirements if req.name]
                logger.info(f"🔍 Found {len(requirement_names)} existing requirements in project ID {project_id}: {requirement_names}")
                return requirement_names
            finally:
                db.close()

        except Exception as e:
            logger.warning(f"⚠️ Failed to get project requirements context: {e}")
            return []

    def _find_conflicting_requirement(self, keyword: str, file_path: str, project_requirements: List[str], current_requirement: str) -> Optional[str]:
        """Find if the file path suggests it belongs to a different requirement from the project."""
        for req_name in project_requirements:
            if req_name.lower() == current_requirement.lower():
                continue  # Skip the current requirement

            req_keywords = req_name.lower().split()
            # Check if any keyword from this requirement appears in the file path
            for req_keyword in req_keywords:
                if len(req_keyword) > 3 and req_keyword in file_path:
                    # Found a conflicting requirement
                    return req_name
        return None

    async def _find_target_test_file(self, test_files: List[Dict], requirement_name: str, new_test_cases: List[Any], project_id: int = None) -> Dict[str, Any]:
        """Find the most appropriate test file to add new test cases using semantic matching with project-wide context."""
        if not test_files:
            return {
                'action': 'create_new',
                'suggested_filename': f"test_{requirement_name.lower().replace(' ', '_')}.py"
            }

        # Get project-wide requirement context for better semantic analysis
        project_requirements = self._get_project_requirements_context(project_id) if project_id else []

        # First, try semantic matching using embeddings and LLM validation with project context
        semantic_match = await self._find_semantically_matching_test_file(
            test_files, requirement_name, new_test_cases, project_requirements
        )

        if semantic_match and semantic_match.get('confidence', 0) > 0.7:
            logger.info(f"Found semantic match: {semantic_match['path']} (confidence: {semantic_match['confidence']:.3f})")
            return {
                'action': 'append_to_existing',
                'file_path': semantic_match['path'],
                'existing_methods': semantic_match['test_methods'],
                'file_content_preview': semantic_match['content'][:1000],
                'match_reason': f'Semantic analysis (confidence: {semantic_match["confidence"]:.3f})',
                'confidence': semantic_match['confidence']
            }

        # Fall back to intelligent keyword matching with project context if semantic matching fails or has low confidence
        logger.info("Falling back to intelligent keyword matching for test file selection")
        requirement_keywords = requirement_name.lower().split()

        best_match = None
        best_score = 0

        for test_file in test_files:
            file_path_lower = test_file['path'].lower()

            # Exact keyword matching with higher weight for exact matches
            exact_matches = sum(2 for keyword in requirement_keywords if keyword in file_path_lower)

            # Intelligent partial matching with project context awareness
            partial_matches = 0
            for keyword in requirement_keywords:
                # Use project requirements context to avoid cross-functional mapping
                if project_requirements:
                    # Check if this file might belong to a different requirement
                    conflicting_requirement = self._find_conflicting_requirement(
                        keyword, file_path_lower, project_requirements, requirement_name
                    )
                    if conflicting_requirement:
                        logger.info(f"🚫 Avoiding cross-functional mapping: '{requirement_name}' to file containing '{conflicting_requirement}' functionality")
                        partial_matches -= 2  # Strong negative score for cross-functional mapping
                        continue

                # Traditional negative scoring for known conflicts
                if keyword == "registration" and "login" in file_path_lower:
                    partial_matches -= 1
                elif keyword == "login" and "registration" in file_path_lower:
                    partial_matches -= 1
                elif any(related in file_path_lower for related in [keyword[:4], keyword[:3]] if len(keyword) > 3):
                    partial_matches += 0.5

            total_score = exact_matches + partial_matches

            if total_score > best_score and total_score > 0:
                best_score = total_score
                best_match = test_file

        if best_match and best_score > 0:
            logger.info(f"Found intelligent keyword match: {best_match['path']} (score: {best_score})")
            return {
                'action': 'append_to_existing',
                'file_path': best_match['path'],
                'existing_methods': best_match['test_methods'],
                'file_content_preview': best_match['content'][:1000],
                'match_reason': f'Intelligent keyword matching (score: {best_score})',
                'confidence': 0.6 if best_score >= 2 else 0.4  # Higher confidence for exact matches
            }

        # If no good match found, create a new file with intelligent naming
        logger.info("No suitable existing test file found, suggesting new file creation")

        # Create intelligent filename based on requirement and language
        clean_name = requirement_name.lower().replace(' ', '_').replace('-', '_')

        # Determine file extension based on language
        if 'js' in str(test_files[0]['path']).lower() if test_files else False:
            suggested_filename = f"tests/{clean_name}Test.js"
        elif 'py' in str(test_files[0]['path']).lower() if test_files else False:
            suggested_filename = f"tests/test_{clean_name}.py"
        else:
            # Default to JavaScript for web testing
            suggested_filename = f"tests/{clean_name}Test.js"

        return {
            'action': 'create_new',
            'suggested_filename': suggested_filename,
            'match_reason': 'No suitable existing file found - creating new file with intelligent naming',
            'confidence': 1.0  # High confidence in creating new file
        }

    async def _determine_generation_strategy(self, test_files: List[Dict], requirement_name: str, new_test_cases: List[Any], project_id: int = None) -> Dict[str, Any]:
        """Determine the best strategy for generating new test cases using intelligent analysis."""
        if not test_files:
            # Generate intelligent filename when no existing files
            clean_name = requirement_name.lower().replace(' ', '_').replace('-', '_')
            suggested_filename = f"tests/{clean_name}Test.js"  # Default to JavaScript

            return {
                'strategy': 'create_new_file',
                'suggested_filename': suggested_filename,
                'reason': 'No existing test files found - creating new file with intelligent naming'
            }

        # Use the same intelligent logic as _find_target_test_file
        target_file_result = await self._find_target_test_file(test_files, requirement_name, new_test_cases, project_id)

        if target_file_result.get('action') == 'append_to_existing':
            return {
                'strategy': 'append_to_existing_file',
                'target_file': target_file_result['file_path'],
                'reason': target_file_result.get('match_reason', 'Found suitable existing test file'),
                'confidence': target_file_result.get('confidence', 0.5)
            }
        elif target_file_result.get('action') == 'create_new':
            return {
                'strategy': 'create_new_file',
                'suggested_filename': target_file_result.get('suggested_filename'),
                'reason': 'No suitable existing test file found - creating new file with intelligent naming'
            }
        else:
            # Fallback to creating new file
            return {
                'strategy': 'create_new_file',
                'reason': 'Unable to determine suitable existing file'
            }

# Global instance
code_embedding_service = CodeEmbeddingService()
