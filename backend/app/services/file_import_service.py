import os
import uuid
import tempfile
from typing import Dict, List, Optional, Any
import pandas as pd
from fastapi import UploadFile, HTTPException
from app.schemas.project import FileUploadResponse, SheetPreview, ColumnMapping, ImportedTestCase, ImportedRequirement

class FileImportService:
    def __init__(self):
        self.temp_files: Dict[str, str] = {}  # file_id -> file_path mapping
        
    async def upload_file(self, file: UploadFile) -> FileUploadResponse:
        """Upload and parse Excel file, return basic info about sheets."""
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
            
        # Validate file extension
        allowed_extensions = ['.xlsx', '.xls']
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file format. Allowed formats: {', '.join(allowed_extensions)}"
            )
        
        # Generate unique file ID
        file_id = str(uuid.uuid4())
        
        # Save file to temporary location
        temp_dir = tempfile.gettempdir()
        temp_path = os.path.join(temp_dir, f"{file_id}_{file.filename}")
        
        try:
            with open(temp_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            
            # Store file path for later use
            self.temp_files[file_id] = temp_path
            
            # Parse file to get sheet information
            excel_file = pd.ExcelFile(temp_path)
            sheets = excel_file.sheet_names
            # Get total rows from the first sheet
            df = pd.read_excel(temp_path, sheet_name=sheets[0])
            total_rows = len(df)
            
            return FileUploadResponse(
                file_id=file_id,
                filename=file.filename,
                sheets=sheets,
                total_rows=total_rows
            )
            
        except pd.errors.EmptyDataError:
            # Clean up temp file if parsing failed
            if os.path.exists(temp_path):
                os.remove(temp_path)
            if file_id in self.temp_files:
                del self.temp_files[file_id]
            raise HTTPException(status_code=400, detail="The uploaded file is empty or contains no data")
        except pd.errors.ParserError as e:
            # Clean up temp file if parsing failed
            if os.path.exists(temp_path):
                os.remove(temp_path)
            if file_id in self.temp_files:
                del self.temp_files[file_id]
            raise HTTPException(status_code=400, detail=f"Failed to parse file format: {str(e)}")
        except Exception as e:
            # Clean up temp file if parsing failed
            if os.path.exists(temp_path):
                os.remove(temp_path)
            if file_id in self.temp_files:
                del self.temp_files[file_id]

            error_msg = str(e)
            if "No such file or directory" in error_msg:
                raise HTTPException(status_code=400, detail="File could not be read. Please ensure it's a valid Excel file")
            elif "Unsupported format" in error_msg:
                raise HTTPException(status_code=400, detail="Unsupported file format. Please upload an Excel (.xlsx, .xls) file")
            else:
                raise HTTPException(status_code=400, detail=f"Failed to process file: {error_msg}")
    
    def get_sheet_preview(self, file_id: str, sheet_name: str, preview_rows: int = 5, starting_row: int = 1) -> SheetPreview:
        """Get preview of a specific sheet with headers and sample rows."""
        if file_id not in self.temp_files:
            raise HTTPException(status_code=404, detail="File not found")

        file_path = self.temp_files[file_id]

        try:
            if starting_row > 1:
                # Read raw data first
                df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None)

                # User provides 1-based starting_row (e.g., 10)
                # Headers are in the row BEFORE starting_row (e.g., row 9)
                # Convert to 0-based indexing
                header_row_index = starting_row - 2  # starting_row=10 -> header_row_index=8 (row 9 in 1-based)
                data_start_index = starting_row - 1   # starting_row=10 -> data_start_index=9 (row 10 in 1-based)

                if header_row_index >= len(df_raw):
                    raise HTTPException(status_code=400, detail=f"Header row {header_row_index + 1} is beyond the sheet data")

                headers = df_raw.iloc[header_row_index].fillna('').astype(str).tolist()

                # Extract data rows (starting from starting_row)
                if data_start_index < len(df_raw):
                    df_data = df_raw.iloc[data_start_index:].copy()
                    df_data.columns = headers
                    df_data = df_data.reset_index(drop=True)
                else:
                    # No data rows, create empty dataframe with headers
                    df_data = pd.DataFrame(columns=headers)

                # Get sample rows
                sample_data = df_data.head(preview_rows).fillna('').astype(str)
                sample_rows = sample_data.values.tolist()

                return SheetPreview(
                    sheet_name=sheet_name,
                    headers=headers,
                    sample_rows=sample_rows,
                    total_rows=len(df_data)
                )
            else:
                # Default behavior for starting_row = 1
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                headers = df.columns.tolist()
                sample_data = df.head(preview_rows).fillna('').astype(str)
                sample_rows = sample_data.values.tolist()

                return SheetPreview(
                    sheet_name=sheet_name,
                    headers=headers,
                    sample_rows=sample_rows,
                    total_rows=len(df)
                )

        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Failed to preview sheet: {str(e)}")
    
    def parse_and_import(self, file_id: str, selected_sheets: List[str],
                        column_mapping: ColumnMapping, existing_requirements: Optional[List[str]] = None) -> List[ImportedRequirement]:
        """Parse selected sheets and extract test cases based on column mapping."""
        if file_id not in self.temp_files:
            raise HTTPException(status_code=404, detail="File not found")

        file_path = self.temp_files[file_id]

        try:
            # If submodule column is specified, group across all sheets by submodule
            if column_mapping.submodule_column:
                return self._parse_with_submodule_grouping(file_path, selected_sheets, column_mapping)
            else:
                # No submodule grouping, treat each sheet as one requirement
                return self._parse_without_submodule_grouping(file_path, selected_sheets, column_mapping)

        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Failed to parse file: {str(e)}")

    def _parse_with_submodule_grouping(self, file_path: str, selected_sheets: List[str],
                                     column_mapping: ColumnMapping) -> List[ImportedRequirement]:
        """Parse sheets with submodule grouping - creates one requirement per unique submodule across all sheets."""
        submodule_test_cases = {}  # Dict to group test cases by submodule
        requirement_counter = 1

        for sheet_name in selected_sheets:
            # Read the sheet with proper header handling
            if column_mapping.starting_row > 1:
                # Read raw data first
                df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None)

                # User provides 1-based starting_row (e.g., 10)
                # Headers are in the row BEFORE starting_row (e.g., row 9)
                header_row_index = column_mapping.starting_row - 2  # starting_row=10 -> header_row_index=8 (row 9 in 1-based)
                data_start_index = column_mapping.starting_row - 1   # starting_row=10 -> data_start_index=9 (row 10 in 1-based)

                headers = df_raw.iloc[header_row_index].fillna('').astype(str).tolist()

                # Extract data rows (starting from starting_row)
                if data_start_index < len(df_raw):
                    df = df_raw.iloc[data_start_index:].copy()
                    df.columns = headers
                    df = df.reset_index(drop=True)
                else:
                    # No data rows
                    df = pd.DataFrame(columns=headers)
            else:
                # Default behavior
                df = pd.read_excel(file_path, sheet_name=sheet_name)

            # Map submodule column reference to actual DataFrame column
            df_columns = list(df.columns)
            submodule_col = self._map_column_to_dataframe(column_mapping.submodule_column, df_columns)

            # Check if submodule column exists in this sheet
            if not submodule_col or submodule_col not in df.columns:
                continue

            # Handle Excel grouped/merged cells by forward-filling empty submodule values
            df[submodule_col] = df[submodule_col].ffill()

            # Also handle empty strings by forward-filling
            mask = df[submodule_col].astype(str).str.strip() == ''
            df.loc[mask, submodule_col] = pd.NA
            df[submodule_col] = df[submodule_col].ffill()

            # Group by submodule after forward-filling
            grouped = df.groupby(submodule_col)

            for submodule_name, group in grouped:
                # Handle missing or empty submodule names
                if pd.isna(submodule_name) or str(submodule_name).strip() == '':
                    requirement_name = f"{sheet_name}_requirement{requirement_counter:02d}"
                    requirement_counter += 1
                else:
                    requirement_name = f"{sheet_name}_{str(submodule_name).strip()}"

                # Extract test cases from this group
                test_cases = self._extract_test_cases_from_group(group, column_mapping)

                if test_cases:
                    if requirement_name in submodule_test_cases:
                        # Add to existing submodule
                        submodule_test_cases[requirement_name].extend(test_cases)
                    else:
                        # Create new submodule entry
                        submodule_test_cases[requirement_name] = test_cases

        # Convert to ImportedRequirement objects
        imported_requirements = []
        for requirement_name, test_cases in submodule_test_cases.items():
            imported_requirements.append(ImportedRequirement(
                name=requirement_name,
                description="",  # Will be generated by LLM
                test_cases=test_cases
            ))

        return imported_requirements

    def _parse_without_submodule_grouping(self, file_path: str, selected_sheets: List[str],
                                        column_mapping: ColumnMapping) -> List[ImportedRequirement]:
        """Parse sheets without submodule grouping - creates one requirement per sheet."""
        imported_requirements = []

        for sheet_name in selected_sheets:
            # Read the sheet with proper header handling
            if column_mapping.starting_row > 1:
                # Read raw data first
                df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None)

                # User provides 1-based starting_row (e.g., 10)
                # Headers are in the row BEFORE starting_row (e.g., row 9)
                header_row_index = column_mapping.starting_row - 2  # starting_row=10 -> header_row_index=8 (row 9 in 1-based)
                data_start_index = column_mapping.starting_row - 1   # starting_row=10 -> data_start_index=9 (row 10 in 1-based)

                headers = df_raw.iloc[header_row_index].fillna('').astype(str).tolist()

                # Extract data rows (starting from starting_row)
                if data_start_index < len(df_raw):
                    df = df_raw.iloc[data_start_index:].copy()
                    df.columns = headers
                    df = df.reset_index(drop=True)
                else:
                    # No data rows
                    df = pd.DataFrame(columns=headers)
            else:
                # Default behavior
                df = pd.read_excel(file_path, sheet_name=sheet_name)

            # Treat entire sheet as one requirement
            requirement_name = sheet_name
            test_cases = self._extract_test_cases_from_group(df, column_mapping)

            if test_cases:  # Only add if there are valid test cases
                imported_requirements.append(ImportedRequirement(
                    name=requirement_name,
                    description="",  # Will be generated by LLM
                    test_cases=test_cases
                ))

        return imported_requirements
    
    def _map_column_to_dataframe(self, column_name: str, df_columns: List[str]) -> Optional[str]:
        """Map frontend column reference to actual DataFrame column name."""
        if not column_name:
            return None

        # Handle column letter mapping (A=0, B=1, C=2, etc.)
        if len(column_name) == 1 and column_name.isalpha():
            col_index = ord(column_name.upper()) - ord('A')
            if 0 <= col_index < len(df_columns):
                return df_columns[col_index]

        # If column_name is already a valid DataFrame column, use it directly
        if column_name in df_columns:
            return column_name

        # If no mapping found, return None (column not found)
        return None

    def _extract_test_cases_from_group(self, group_df: pd.DataFrame,
                                     column_mapping: ColumnMapping) -> List[ImportedTestCase]:
        """Extract test cases from a DataFrame group."""
        test_cases = []
        df_columns = list(group_df.columns)

        # Map frontend column references to actual DataFrame columns
        steps_col = self._map_column_to_dataframe(column_mapping.steps_column, df_columns)
        expected_result_col = self._map_column_to_dataframe(column_mapping.expected_result_column, df_columns) if column_mapping.expected_result_column else None
        title_col = self._map_column_to_dataframe(column_mapping.title_column, df_columns) if column_mapping.title_column else None
        notes_col = self._map_column_to_dataframe(column_mapping.notes_column, df_columns) if column_mapping.notes_column else None

        for _, row in group_df.iterrows():
            # Extract required fields
            steps = str(row.get(steps_col, '')).strip()

            # Skip rows with empty required fields
            if not steps or steps == 'nan':
                continue

            # Extract expected result (now optional)
            expected_result = None
            if expected_result_col and expected_result_col in row:
                expected_result_val = str(row.get(expected_result_col, '')).strip()
                if expected_result_val and expected_result_val != 'nan':
                    expected_result = expected_result_val

            # Extract optional fields
            title = None
            if title_col and title_col in row:
                title_val = str(row.get(title_col, '')).strip()
                if title_val and title_val != 'nan':
                    title = title_val

            notes = None
            if notes_col and notes_col in row:
                notes_val = str(row.get(notes_col, '')).strip()
                if notes_val and notes_val != 'nan':
                    notes = notes_val

            test_cases.append(ImportedTestCase(
                title=title,
                steps=steps,
                expected_result=expected_result,
                notes=notes
            ))
        
        return test_cases
    
    def cleanup_file(self, file_id: str):
        """Clean up temporary file."""
        if file_id in self.temp_files:
            file_path = self.temp_files[file_id]
            if os.path.exists(file_path):
                os.remove(file_path)
            del self.temp_files[file_id]
    
    def cleanup_all_files(self):
        """Clean up all temporary files."""
        for file_id in list(self.temp_files.keys()):
            self.cleanup_file(file_id)

# Global instance
file_import_service = FileImportService()
