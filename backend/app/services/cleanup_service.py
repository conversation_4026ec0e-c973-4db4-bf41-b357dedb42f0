import asyncio
import logging
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any
from pathlib import Path
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.crud.code_generation import get_code_generation_sessions_by_status
from app.models.code_generation import CodeGenerationStatus
from app.services.git_service import git_service
from app.services.file_management_service import file_management_service

logger = logging.getLogger(__name__)

class CleanupService:
    """Service for cleaning up orphaned projects and temporary files."""
    
    def __init__(self):
        self.cleanup_interval = 3600  # 1 hour
        self.max_project_age = 24 * 3600  # 24 hours
        self.max_temp_file_age = 6 * 3600  # 6 hours
        self._running = False
        self._cleanup_task = None

    async def start_periodic_cleanup(self):
        """Start the periodic cleanup task."""
        if self._running:
            logger.warning("Cleanup service is already running")
            return
        
        self._running = True
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("Started periodic cleanup service")

    async def stop_periodic_cleanup(self):
        """Stop the periodic cleanup task."""
        if not self._running:
            return
        
        self._running = False
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Stopped periodic cleanup service")

    async def _cleanup_loop(self):
        """Main cleanup loop."""
        while self._running:
            try:
                await self.cleanup_orphaned_projects()
                await self.cleanup_temporary_files()
                await self.cleanup_failed_sessions()
                
                # Wait for next cleanup cycle
                await asyncio.sleep(self.cleanup_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

    async def cleanup_orphaned_projects(self) -> Dict[str, Any]:
        """Clean up orphaned project directories."""
        try:
            logger.info("🧹 Starting orphaned projects cleanup")
            
            # Get all project directories
            projects_base_path = Path(git_service.base_path)
            if not projects_base_path.exists():
                return {"success": True, "cleaned_projects": 0, "message": "No projects directory found"}
            
            cleaned_projects = []
            failed_cleanups = []
            current_time = time.time()
            
            # Check each project directory
            for project_dir in projects_base_path.iterdir():
                if not project_dir.is_dir():
                    continue
                
                try:
                    # Check if project directory is old enough to be cleaned
                    dir_age = current_time - project_dir.stat().st_mtime
                    
                    if dir_age > self.max_project_age:
                        # Check if there are any active sessions for this project
                        project_name = project_dir.name
                        
                        with SessionLocal() as db:
                            active_sessions = self._get_active_sessions_for_project(db, project_name)
                            
                            if not active_sessions:
                                # No active sessions, safe to clean up
                                success = git_service.cleanup_project(project_name)
                                
                                if success:
                                    cleaned_projects.append({
                                        "project_name": project_name,
                                        "age_hours": dir_age / 3600,
                                        "cleaned_at": datetime.now().isoformat()
                                    })
                                    logger.info(f"🗑️ Cleaned up orphaned project: {project_name}")
                                else:
                                    failed_cleanups.append({
                                        "project_name": project_name,
                                        "error": "Failed to cleanup project directory"
                                    })
                            else:
                                logger.debug(f"⏭️ Skipping project {project_name} - has {len(active_sessions)} active sessions")
                
                except Exception as e:
                    failed_cleanups.append({
                        "project_name": project_dir.name,
                        "error": str(e)
                    })
                    logger.warning(f"Failed to process project directory {project_dir.name}: {e}")
            
            result = {
                "success": True,
                "cleaned_projects": len(cleaned_projects),
                "failed_cleanups": len(failed_cleanups),
                "details": {
                    "cleaned": cleaned_projects,
                    "failed": failed_cleanups
                }
            }
            
            if cleaned_projects:
                logger.info(f"✅ Cleaned up {len(cleaned_projects)} orphaned projects")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Error during orphaned projects cleanup: {e}")
            return {
                "success": False,
                "error": str(e),
                "cleaned_projects": 0,
                "failed_cleanups": 0
            }

    async def cleanup_temporary_files(self) -> Dict[str, Any]:
        """Clean up temporary files."""
        try:
            logger.debug("🧹 Starting temporary files cleanup")
            
            # Clean up file management service temp files
            result = await file_management_service.cleanup_temp_files(
                older_than_hours=self.max_temp_file_age / 3600
            )
            
            if result.get("removed_files", 0) > 0:
                logger.info(f"🗑️ Cleaned up {result['removed_files']} temporary files")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Error during temporary files cleanup: {e}")
            return {
                "success": False,
                "error": str(e),
                "removed_files": 0
            }

    async def cleanup_failed_sessions(self) -> Dict[str, Any]:
        """Clean up projects associated with failed or old sessions."""
        try:
            logger.debug("🧹 Starting failed sessions cleanup")
            
            cleaned_sessions = []
            failed_cleanups = []
            
            with SessionLocal() as db:
                # Get failed sessions older than 1 hour
                cutoff_time = datetime.now() - timedelta(hours=1)
                
                failed_sessions = db.query(
                    # This would need to be implemented in the CRUD layer
                    # For now, we'll skip this part
                ).all() if hasattr(db, 'query') else []
                
                for session in failed_sessions:
                    try:
                        # Get project name from session
                        project_name = getattr(session, 'project_name', None)
                        
                        if project_name:
                            # Clean up project directory
                            success = git_service.cleanup_project(project_name)
                            
                            if success:
                                cleaned_sessions.append({
                                    "session_id": session.session_id,
                                    "project_name": project_name,
                                    "status": session.status,
                                    "cleaned_at": datetime.now().isoformat()
                                })
                            else:
                                failed_cleanups.append({
                                    "session_id": session.session_id,
                                    "project_name": project_name,
                                    "error": "Failed to cleanup project"
                                })
                    
                    except Exception as e:
                        failed_cleanups.append({
                            "session_id": getattr(session, 'session_id', 'unknown'),
                            "error": str(e)
                        })
            
            result = {
                "success": True,
                "cleaned_sessions": len(cleaned_sessions),
                "failed_cleanups": len(failed_cleanups),
                "details": {
                    "cleaned": cleaned_sessions,
                    "failed": failed_cleanups
                }
            }
            
            if cleaned_sessions:
                logger.info(f"✅ Cleaned up {len(cleaned_sessions)} failed session projects")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Error during failed sessions cleanup: {e}")
            return {
                "success": False,
                "error": str(e),
                "cleaned_sessions": 0
            }

    def _get_active_sessions_for_project(self, db: Session, project_name: str) -> List[Any]:
        """Get active code generation sessions for a project."""
        try:
            # This is a simplified check - in a real implementation,
            # you'd need to join with the projects table to match by name
            active_statuses = [
                CodeGenerationStatus.PENDING,
                CodeGenerationStatus.IN_PROGRESS
            ]
            
            # For now, return empty list as we don't have the exact query structure
            return []
            
        except Exception as e:
            logger.warning(f"Could not check active sessions for project {project_name}: {e}")
            return []

    async def manual_cleanup(self, project_name: str = None) -> Dict[str, Any]:
        """Perform manual cleanup of specific project or all projects."""
        try:
            if project_name:
                # Clean up specific project
                success = git_service.cleanup_project(project_name)
                return {
                    "success": success,
                    "message": f"Project {project_name} cleanup {'successful' if success else 'failed'}"
                }
            else:
                # Clean up all orphaned projects
                results = await self.cleanup_orphaned_projects()
                temp_results = await self.cleanup_temporary_files()
                
                return {
                    "success": True,
                    "projects_cleanup": results,
                    "temp_files_cleanup": temp_results,
                    "message": "Manual cleanup completed"
                }
                
        except Exception as e:
            logger.error(f"❌ Error during manual cleanup: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Manual cleanup failed"
            }

# Global instance
cleanup_service = CleanupService()
