#!/usr/bin/env python3
"""
Create sample Excel files for testing Project Type 2 import functionality
"""
import pandas as pd
import os

def create_sample_excel_files():
    """Create sample Excel files for testing"""
    
    # Sample 1: Login and Registration Module
    login_data = {
        'Submodule': ['Login', 'Login', 'Login', 'Registration', 'Registration', 'Registration'],
        'Test Title': [
            'Valid Login Test',
            'Invalid Username Test', 
            'Invalid Password Test',
            'Valid Registration Test',
            'Duplicate Email Test',
            'Invalid Email Format Test'
        ],
        'Test Steps': [
            'Enter valid username "testuser" and password "password123", click Login button',
            'Enter invalid username "wronguser" and valid password "password123", click Login button',
            'Enter valid username "testuser" and invalid password "wrongpass", click Login button',
            'Fill registration form with valid data: name="<PERSON> Doe", email="<EMAIL>", password="password123", click Register button',
            'Fill registration form with existing email "<EMAIL>", click Register button',
            'Fill registration form with invalid email format "invalid-email", click Register button'
        ],
        'Expected Result': [
            'User should be redirected to dashboard with welcome message',
            'Error message "Invalid username or password" should be displayed',
            'Error message "Invalid username or password" should be displayed',
            'Account should be created successfully and confirmation email sent',
            'Error message "Email already exists" should be displayed',
            'Email validation error "Please enter a valid email address" should be shown'
        ],
        'Notes': [
            'Test with different browsers',
            'Check error message styling',
            'Verify password masking',
            'Verify email confirmation flow',
            'Test case sensitivity',
            'Test various invalid formats'
        ]
    }
    
    # Sample 2: E-commerce Shopping Cart
    shopping_data = {
        'Submodule': ['Product Search', 'Product Search', 'Shopping Cart', 'Shopping Cart', 'Checkout'],
        'Test Title': [
            'Search Valid Product',
            'Search Non-existent Product',
            'Add Product to Cart',
            'Remove Product from Cart',
            'Complete Purchase'
        ],
        'Test Steps': [
            'Enter "laptop" in search box and click Search button',
            'Enter "xyz123nonexistent" in search box and click Search button',
            'Navigate to product page, select quantity 2, click "Add to Cart" button',
            'Go to cart page, click "Remove" button next to product',
            'Fill shipping details, select payment method, click "Place Order" button'
        ],
        'Expected Result': [
            'List of laptop products should be displayed with filters',
            'Message "No products found" should be displayed with search suggestions',
            'Product should be added to cart with correct quantity and price',
            'Product should be removed from cart and total should update',
            'Order confirmation page should be displayed with order number'
        ],
        'Notes': [
            'Test search filters',
            'Test search suggestions',
            'Verify cart persistence',
            'Test cart calculations',
            'Test payment integration'
        ]
    }
    
    # Create Excel files
    output_dir = "sample_excel_files"
    os.makedirs(output_dir, exist_ok=True)
    
    # Create multi-sheet Excel file
    with pd.ExcelWriter(f"{output_dir}/sample_test_cases_multi_sheet.xlsx", engine='openpyxl') as writer:
        pd.DataFrame(login_data).to_excel(writer, sheet_name='Login_Registration', index=False)
        pd.DataFrame(shopping_data).to_excel(writer, sheet_name='Shopping_Cart', index=False)
    
    # Create single sheet Excel file
    pd.DataFrame(login_data).to_excel(f"{output_dir}/sample_login_tests.xlsx", index=False)
    
    print("✅ Sample Excel files created:")
    print(f"   📁 {output_dir}/sample_test_cases_multi_sheet.xlsx (2 sheets)")
    print(f"   📁 {output_dir}/sample_login_tests.xlsx (1 sheet)")
    print()
    print("📋 File Structure:")
    print("   - Submodule: Groups test cases within a sheet")
    print("   - Test Title: Name of the test case")
    print("   - Test Steps: Detailed steps to execute")
    print("   - Expected Result: What should happen")
    print("   - Notes: Additional information")
    print()
    print("🧪 Test Instructions:")
    print("1. Log into IntelliTest application")
    print("2. Create a new Project Type 2 (Import Test Cases)")
    print("3. Upload one of the sample Excel files")
    print("4. Map columns correctly")
    print("5. Import and verify AI-generated requirements")

if __name__ == "__main__":
    create_sample_excel_files()
