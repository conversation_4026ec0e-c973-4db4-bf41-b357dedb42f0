#!/usr/bin/env python3
"""
Simple test script to validate service optimizations without full app context.
"""
import asyncio
import sys
import os
from unittest.mock import Mock, AsyncMock, patch

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_rag_service_optimizations():
    """Test RAG service optimizations."""
    print("🧪 Testing RAG Service Optimizations...")
    
    # Test payload index creation
    from services.rag_service import RAGService
    
    service = RAGService()
    service.client = Mock()
    service.is_available = True
    
    # Test payload index creation
    service._create_payload_indexes()
    print("✅ Payload indexes creation method works")
    
    # Test optimized deletion
    service.delete_test_case(123)
    service.delete_requirement(456)
    print("✅ Optimized deletion methods work")
    
    # Test bulk operations
    service.encoder = Mock()
    service.encoder.encode.return_value = [[0.1, 0.2], [0.3, 0.4]]
    
    requirements_data = [
        {"requirement_id": 1, "project_id": 100, "text": "Test 1"},
        {"requirement_id": 2, "project_id": 100, "text": "Test 2"}
    ]
    service.add_requirements_bulk(requirements_data)
    print("✅ Bulk requirements addition works")
    
    print("🎉 RAG Service optimizations validated!")


async def test_ollama_service_optimizations():
    """Test Ollama service optimizations."""
    print("🧪 Testing Ollama Service Optimizations...")
    
    with patch('httpx.AsyncClient') as mock_client_class:
        mock_client = AsyncMock()
        mock_client_class.return_value = mock_client
        
        from services.llm_service import OllamaService
        
        service = OllamaService()
        service.client = mock_client
        service._closed = False
        
        # Test persistent client usage
        mock_response = Mock()
        mock_response.json.return_value = {"response": "test response"}
        mock_client.post.return_value = mock_response
        
        await service._make_request("test prompt")
        print("✅ Persistent HTTP client works")
        
        # Test cleanup
        await service.close()
        print("✅ Client cleanup works")
        
        print("🎉 Ollama Service optimizations validated!")


def test_code_embedding_service_optimizations():
    """Test Code Embedding Service optimizations."""
    print("🧪 Testing Code Embedding Service Optimizations...")
    
    from services.code_embedding_service import CodeEmbeddingService, ProgrammingLanguage
    
    service = CodeEmbeddingService()
    service.client = Mock()
    service.encoder = Mock()
    service.is_available = True
    
    # Test optimized deletion
    service.delete_project_embeddings(123)
    print("✅ Optimized project deletion works")
    
    # Test semaphore initialization
    assert hasattr(service, '_llm_semaphore')
    print("✅ LLM semaphore initialized")
    
    print("🎉 Code Embedding Service optimizations validated!")


async def main():
    """Run all optimization tests."""
    print("🚀 Starting Service Optimization Validation Tests\n")
    
    try:
        # Test RAG Service
        test_rag_service_optimizations()
        print()
        
        # Test Ollama Service
        await test_ollama_service_optimizations()
        print()
        
        # Test Code Embedding Service
        test_code_embedding_service_optimizations()
        print()
        
        print("🎉 All optimization tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
