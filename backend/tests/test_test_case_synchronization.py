import pytest
from unittest.mock import Mock
from app.api.api_v1.endpoints.file_import import (
    normalize_text_for_comparison,
    are_test_cases_similar,
    find_matching_test_case,
    get_next_test_case_number,
    synchronize_test_cases
)
from app.schemas.requirement import TestCase
from app.schemas.project import ImportedTestCase


class TestTextNormalization:
    """Test text normalization for comparison."""
    
    def test_normalize_empty_text(self):
        assert normalize_text_for_comparison("") == ""
        assert normalize_text_for_comparison(None) == ""
    
    def test_normalize_whitespace(self):
        text = "  Step 1: Login\n  Step 2: Navigate  \n\n  Step 3: Verify  "
        expected = "step 1: login step 2: navigate step 3: verify"
        assert normalize_text_for_comparison(text) == expected
    
    def test_normalize_case(self):
        text = "ENTER USERNAME AND PASSWORD"
        expected = "enter username and password"
        assert normalize_text_for_comparison(text) == expected


class TestTestCaseSimilarity:
    """Test test case similarity comparison."""
    
    def create_mock_test_case(self, steps: str, title: str = "Test Case"):
        """Helper to create mock test case."""
        mock_test_case = Mock()
        mock_test_case.steps = steps
        mock_test_case.title = title
        return mock_test_case
    
    def create_imported_test_case(self, steps: str, title: str = "Test Case"):
        """Helper to create imported test case."""
        return ImportedTestCase(
            title=title,
            steps=steps,
            expected_result="Expected result",
            notes="Notes"
        )
    
    def test_identical_steps(self):
        existing = self.create_mock_test_case("Enter username and password")
        imported = self.create_imported_test_case("Enter username and password")
        
        assert are_test_cases_similar(existing, imported) is True
    
    def test_identical_steps_different_whitespace(self):
        existing = self.create_mock_test_case("Enter username and password")
        imported = self.create_imported_test_case("  Enter   username  and   password  ")
        
        assert are_test_cases_similar(existing, imported) is True
    
    def test_identical_steps_different_case(self):
        existing = self.create_mock_test_case("Enter username and password")
        imported = self.create_imported_test_case("ENTER USERNAME AND PASSWORD")
        
        assert are_test_cases_similar(existing, imported) is True
    
    def test_similar_steps_high_similarity(self):
        existing = self.create_mock_test_case("Enter valid username and password then click login")
        imported = self.create_imported_test_case("Enter valid username and password click login")
        
        assert are_test_cases_similar(existing, imported) is True
    
    def test_different_steps_low_similarity(self):
        existing = self.create_mock_test_case("Enter username and password")
        imported = self.create_imported_test_case("Navigate to settings page")
        
        assert are_test_cases_similar(existing, imported) is False
    
    def test_empty_steps(self):
        existing = self.create_mock_test_case("")
        imported = self.create_imported_test_case("")
        
        assert are_test_cases_similar(existing, imported) is True
    
    def test_one_empty_step(self):
        existing = self.create_mock_test_case("Enter username")
        imported = self.create_imported_test_case("")
        
        assert are_test_cases_similar(existing, imported) is False


class TestFindMatchingTestCase:
    """Test finding matching test cases."""
    
    def create_mock_test_case(self, steps: str, title: str = "Test Case"):
        """Helper to create mock test case."""
        mock_test_case = Mock()
        mock_test_case.steps = steps
        mock_test_case.title = title
        return mock_test_case
    
    def create_imported_test_case(self, steps: str, title: str = "Test Case"):
        """Helper to create imported test case."""
        return ImportedTestCase(
            title=title,
            steps=steps,
            expected_result="Expected result",
            notes="Notes"
        )
    
    def test_find_exact_match(self):
        existing_cases = [
            self.create_mock_test_case("Login with valid credentials"),
            self.create_mock_test_case("Login with invalid credentials"),
            self.create_mock_test_case("Logout from application")
        ]
        
        imported = self.create_imported_test_case("Login with valid credentials")
        match = find_matching_test_case(imported, existing_cases)
        
        assert match is not None
        assert match.steps == "Login with valid credentials"
    
    def test_find_no_match(self):
        existing_cases = [
            self.create_mock_test_case("Login with valid credentials"),
            self.create_mock_test_case("Logout from application")
        ]
        
        imported = self.create_imported_test_case("Navigate to settings page")
        match = find_matching_test_case(imported, existing_cases)
        
        assert match is None
    
    def test_find_match_empty_list(self):
        existing_cases = []
        imported = self.create_imported_test_case("Login with valid credentials")
        match = find_matching_test_case(imported, existing_cases)
        
        assert match is None


class TestGetNextTestCaseNumber:
    """Test getting next test case number."""
    
    def create_mock_test_case(self, title: str):
        """Helper to create mock test case."""
        mock_test_case = Mock()
        mock_test_case.title = title
        return mock_test_case
    
    def test_empty_list(self):
        assert get_next_test_case_number([]) == 1
    
    def test_sequential_numbers(self):
        existing_cases = [
            self.create_mock_test_case("Test Case 01"),
            self.create_mock_test_case("Test Case 02"),
            self.create_mock_test_case("Test Case 03")
        ]
        
        assert get_next_test_case_number(existing_cases) == 4
    
    def test_non_sequential_numbers(self):
        existing_cases = [
            self.create_mock_test_case("Test Case 01"),
            self.create_mock_test_case("Test Case 05"),
            self.create_mock_test_case("Test Case 03")
        ]
        
        assert get_next_test_case_number(existing_cases) == 6
    
    def test_mixed_title_formats(self):
        existing_cases = [
            self.create_mock_test_case("Test Case 01"),
            self.create_mock_test_case("Custom Test Title"),
            self.create_mock_test_case("Test Case 03"),
            self.create_mock_test_case("Another Custom Title")
        ]
        
        assert get_next_test_case_number(existing_cases) == 4
    
    def test_case_insensitive_matching(self):
        existing_cases = [
            self.create_mock_test_case("test case 01"),
            self.create_mock_test_case("TEST CASE 02")
        ]
        
        assert get_next_test_case_number(existing_cases) == 3


# Note: synchronize_test_cases function would require database mocking
# which is more complex and would be better tested in integration tests
