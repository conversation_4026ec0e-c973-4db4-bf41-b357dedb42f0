"""
Test suite for service optimizations to ensure performance improvements work correctly.
"""
import pytest
import asyncio
import uuid
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import List, Dict, Any

from app.services.rag_service import RAGService
from app.services.llm_service import OllamaService
from app.services.code_embedding_service import CodeEmbeddingService, ProgrammingLanguage


class TestRAGServiceOptimizations:
    """Test RAG service optimizations."""

    @pytest.fixture
    def mock_rag_service(self):
        """Create a mock RAG service for testing."""
        service = RAGService()
        service.client = Mock()
        service.encoder = Mock()
        service.is_available = True
        return service

    def test_payload_indexes_creation(self, mock_rag_service):
        """Test that payload indexes are created correctly."""
        mock_rag_service._create_payload_indexes()
        
        # Verify that create_payload_index was called for each expected field
        expected_calls = [
            ("project_id", "INTEGER"),
            ("requirement_id", "INTEGER"), 
            ("test_case_id", "INTEGER"),
            ("type", "KEYWORD")
        ]
        
        assert mock_rag_service.client.create_payload_index.call_count == 4

    def test_optimized_delete_test_case(self, mock_rag_service):
        """Test optimized test case deletion using FilterSelector."""
        test_case_id = 123
        mock_rag_service.delete_test_case(test_case_id)
        
        # Verify that delete was called with FilterSelector instead of scroll+delete
        mock_rag_service.client.delete.assert_called_once()
        call_args = mock_rag_service.client.delete.call_args
        
        # Check that points_selector is a FilterSelector
        assert "points_selector" in call_args.kwargs
        points_selector = call_args.kwargs["points_selector"]
        assert hasattr(points_selector, 'filter')

    def test_optimized_delete_requirement(self, mock_rag_service):
        """Test optimized requirement deletion using FilterSelector."""
        requirement_id = 456
        mock_rag_service.delete_requirement(requirement_id)
        
        # Verify that delete was called with FilterSelector
        mock_rag_service.client.delete.assert_called_once()
        call_args = mock_rag_service.client.delete.call_args
        
        assert "points_selector" in call_args.kwargs
        points_selector = call_args.kwargs["points_selector"]
        assert hasattr(points_selector, 'filter')

    def test_bulk_requirements_addition(self, mock_rag_service):
        """Test bulk requirements addition."""
        requirements_data = [
            {
                "requirement_id": 1,
                "project_id": 100,
                "text": "Test requirement 1",
                "metadata": {"priority": "high"}
            },
            {
                "requirement_id": 2,
                "project_id": 100,
                "text": "Test requirement 2",
                "metadata": {"priority": "medium"}
            }
        ]
        
        # Mock encoder to return embeddings
        mock_rag_service.encoder.encode.return_value = [[0.1, 0.2], [0.3, 0.4]]
        
        mock_rag_service.add_requirements_bulk(requirements_data)
        
        # Verify batch encoding was used
        mock_rag_service.encoder.encode.assert_called_once()
        
        # Verify bulk upsert was called
        mock_rag_service.client.upsert.assert_called_once()
        call_args = mock_rag_service.client.upsert.call_args
        points = call_args.kwargs["points"]
        assert len(points) == 2

    def test_resilient_bulk_test_cases(self, mock_rag_service):
        """Test resilient bulk test case insertion with partial failures."""
        test_cases_data = [
            {
                "test_case_id": 1,
                "requirement_id": 10,
                "project_id": 100,
                "text": "Test case 1"
            },
            {
                "test_case_id": 2,
                "requirement_id": 10,
                "project_id": 100,
                "text": "Test case 2"
            }
        ]
        
        # Mock encoder and simulate bulk failure
        mock_rag_service.encoder.encode.return_value = [[0.1, 0.2], [0.3, 0.4]]
        mock_rag_service.client.upsert.side_effect = Exception("Bulk insert failed")
        
        # Mock individual add_test_case method
        mock_rag_service.add_test_case = Mock()
        
        mock_rag_service.add_test_cases_bulk(test_cases_data)
        
        # Verify fallback to smaller batches was attempted
        assert mock_rag_service.encoder.encode.call_count >= 1


class TestOllamaServiceOptimizations:
    """Test Ollama service HTTP client optimizations."""

    @pytest.fixture
    def mock_ollama_service(self):
        """Create a mock Ollama service for testing."""
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            
            service = OllamaService()
            service.client = mock_client
            service._closed = False
            return service, mock_client

    @pytest.mark.asyncio
    async def test_persistent_client_usage(self, mock_ollama_service):
        """Test that persistent HTTP client is used for requests."""
        service, mock_client = mock_ollama_service
        
        # Mock response
        mock_response = Mock()
        mock_response.json.return_value = {"response": "test response"}
        mock_client.post.return_value = mock_response
        
        # Make multiple requests
        await service._make_request("test prompt 1")
        await service._make_request("test prompt 2")
        
        # Verify the same client instance was used
        assert mock_client.post.call_count == 2

    @pytest.mark.asyncio
    async def test_streaming_request_with_persistent_client(self, mock_ollama_service):
        """Test streaming requests use persistent client."""
        service, mock_client = mock_ollama_service
        
        # Mock streaming response
        mock_stream_response = AsyncMock()
        mock_stream_response.aiter_lines.return_value = [
            '{"response": "chunk1", "done": false}',
            '{"response": "chunk2", "done": true}'
        ]
        mock_client.stream.return_value.__aenter__.return_value = mock_stream_response
        
        # Collect streaming response
        chunks = []
        async for chunk in service._make_streaming_request("test prompt"):
            chunks.append(chunk)
        
        # Verify streaming was used with persistent client
        mock_client.stream.assert_called_once()
        assert chunks == ["chunk1", "chunk2"]

    @pytest.mark.asyncio
    async def test_client_cleanup(self, mock_ollama_service):
        """Test that HTTP client is properly closed."""
        service, mock_client = mock_ollama_service
        
        await service.close()
        
        # Verify client was closed
        mock_client.aclose.assert_called_once()
        assert service._closed is True

    @pytest.mark.asyncio
    async def test_context_manager_usage(self, mock_ollama_service):
        """Test async context manager functionality."""
        service, mock_client = mock_ollama_service
        
        async with service as ctx_service:
            assert ctx_service is service
        
        # Verify client was closed after context exit
        mock_client.aclose.assert_called_once()


class TestCodeEmbeddingServiceOptimizations:
    """Test code embedding service optimizations."""

    @pytest.fixture
    def mock_code_service(self):
        """Create a mock code embedding service for testing."""
        service = CodeEmbeddingService()
        service.client = Mock()
        service.encoder = Mock()
        service.is_available = True
        service._llm_semaphore = asyncio.Semaphore(5)
        return service

    def test_optimized_project_deletion(self, mock_code_service):
        """Test optimized project embeddings deletion."""
        project_id = 123
        mock_code_service.delete_project_embeddings(project_id)
        
        # Verify that delete was called with FilterSelector instead of scroll+delete
        mock_code_service.client.delete.assert_called_once()
        call_args = mock_code_service.client.delete.call_args
        
        assert "points_selector" in call_args.kwargs
        points_selector = call_args.kwargs["points_selector"]
        assert hasattr(points_selector, 'filter')

    @patch('app.services.git_service.git_service')
    def test_batch_repository_embedding(self, mock_git_service, mock_code_service):
        """Test batch processing in repository embedding."""
        # Mock git service responses
        mock_git_service.get_repository_files.return_value = [
            {"path": "test1.py", "is_text": True, "size": 100},
            {"path": "test2.py", "is_text": True, "size": 200}
        ]
        mock_git_service.read_file_content.side_effect = [
            "def test1(): pass",
            "def test2(): pass"
        ]
        
        # Mock encoder for batch processing
        mock_code_service.encoder.encode.return_value = [[0.1, 0.2], [0.3, 0.4]]
        mock_code_service._split_code_into_chunks = Mock(side_effect=lambda content, path: [content])
        mock_code_service._should_embed_file = Mock(return_value=True)
        
        result = mock_code_service.embed_repository(
            project_id=1,
            project_name="test_project",
            language=ProgrammingLanguage.PYTHON
        )
        
        # Verify batch processing was used
        mock_code_service.encoder.encode.assert_called_once()
        mock_code_service.client.upsert.assert_called_once()
        
        # Verify result structure
        assert result["success"] is True
        assert "total_chunks" in result

    @pytest.mark.asyncio
    async def test_semaphore_controlled_llm_calls(self, mock_code_service):
        """Test that LLM calls are controlled by semaphore."""
        from app.models.code_entity import CodeEntity
        
        # Create test entities
        entities = [
            CodeEntity(
                name=f"test_entity_{i}",
                type="function",
                file_path="test.py",
                start_line=i,
                end_line=i+5,
                signature=f"def test_entity_{i}():",
                docstring=None,
                dependencies=[],
                complexity_score=1.0,
                semantic_tags=[]
            ) for i in range(3)
        ]
        
        file_content = "def test_entity_0(): pass\ndef test_entity_1(): pass\ndef test_entity_2(): pass"
        
        # Mock the semaphore-controlled analysis method
        mock_code_service._analyze_entity_with_semaphore = AsyncMock(
            return_value={"semantic_tags": ["test_tag"]}
        )
        
        result = await mock_code_service._enhance_entities_with_semantics(entities, file_content)
        
        # Verify that semaphore-controlled method was called
        assert mock_code_service._analyze_entity_with_semaphore.call_count <= 3
        assert len(result) == 3


if __name__ == "__main__":
    pytest.main([__file__])
