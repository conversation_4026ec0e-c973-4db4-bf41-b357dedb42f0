/**
 * Utility functions for converting markdown to plain text
 */

/**
 * Convert markdown text to plain text by removing common markdown formatting
 * @param markdown The markdown text to convert
 * @returns Plain text without markdown formatting
 */
export function markdownToPlainText(markdown: string): string {
  if (!markdown) return '';

  let text = markdown;

  // Remove bold formatting (**text** or __text__)
  text = text.replace(/\*\*(.*?)\*\*/g, '$1');
  text = text.replace(/__(.*?)__/g, '$1');

  // Remove italic formatting (*text* or _text_)
  text = text.replace(/\*(.*?)\*/g, '$1');
  text = text.replace(/_(.*?)_/g, '$1');

  // Remove strikethrough (~~text~~)
  text = text.replace(/~~(.*?)~~/g, '$1');

  // Remove inline code (`text`)
  text = text.replace(/`(.*?)`/g, '$1');

  // Remove headers (# ## ### etc.)
  text = text.replace(/^#{1,6}\s+/gm, '');

  // Remove horizontal rules (--- or ***)
  text = text.replace(/^[-*]{3,}$/gm, '');

  // Remove blockquotes (> text)
  text = text.replace(/^>\s*/gm, '');

  // Remove list markers (- * + or 1. 2. etc.)
  text = text.replace(/^[\s]*[*+]\s+/gm, '');

  // Remove links [text](url) -> text
  text = text.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1');

  // Remove images ![alt](url) -> alt
  text = text.replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1');

  // Remove code blocks (```code```)
  text = text.replace(/```[\s\S]*?```/g, '');

  // Clean up extra whitespace
  text = text.replace(/\n{3,}/g, '\n\n');
  text = text.trim();

  return text;
}

/**
 * Convert plain text to markdown by adding basic formatting
 * @param text The plain text to convert
 * @returns Text with basic markdown formatting
 */
export function plainTextToMarkdown(text: string): string {
  if (!text) return '';

  // This is a simple conversion - mainly for preserving line breaks
  return text.replace(/\n/g, '\n\n');
}
