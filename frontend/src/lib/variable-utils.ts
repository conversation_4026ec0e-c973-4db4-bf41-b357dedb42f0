interface ProjectVariable {
  id: number;
  key: string;
  value: string;
  description?: string;
}

/**
 * Replace @variable references with their actual values
 * Only replaces @ variables that are preceded by space or at start of text
 */
export function replaceVariableReferences(text: string, variables: ProjectVariable[]): string {
  if (!text || !variables.length) return text;

  let result = text;

  // Create a map for faster lookup
  const variableMap = new Map(variables.map(v => [v.key, v.value]));

  // Replace @variable_name with actual values, only when preceded by space or at start
  result = result.replace(/(^|\s)@([a-zA-Z_]\w*)/g, (match, prefix, variableName) => {
    const value = variableMap.get(variableName);
    return value !== undefined ? prefix + value : match; // Keep original if not found
  });

  return result;
}

/**
 * Parse text to find variable references and their positions
 * Only matches @ variables that are preceded by space or at start of text
 */
export function parseVariableReferences(text: string, variables?: ProjectVariable[]): Array<{
  match: string;
  variableName: string;
  start: number;
  end: number;
}> {
  const references: Array<{
    match: string;
    variableName: string;
    start: number;
    end: number;
  }> = [];

  // Match @ variables that are either at start or preceded by space
  const regex = /(^|\s)@([a-zA-Z_]\w*)/g;
  let match;

  while ((match = regex.exec(text)) !== null) {
    const variableName = match[2];

    // If variables list is provided, only include variables that exist in the list
    if (variables && !isValidVariable(variableName, variables)) {
      continue;
    }

    // Calculate the actual start position of the @ symbol
    const atSymbolStart = match.index + match[1].length;

    references.push({
      match: `@${variableName}`,
      variableName: variableName,
      start: atSymbolStart,
      end: atSymbolStart + variableName.length + 1 // +1 for the @ symbol
    });
  }

  return references;
}

/**
 * Check if a variable name exists in the variables list
 */
export function isValidVariable(variableName: string, variables: ProjectVariable[]): boolean {
  return variables.some(v => v.key === variableName);
}

/**
 * Get variable by name
 */
export function getVariableByName(variableName: string, variables: ProjectVariable[]): ProjectVariable | undefined {
  return variables.find(v => v.key === variableName);
}
