'use client';

import { useState } from 'react';
import { apiClient } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogBody, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BadgePlus, FileSpreadsheet, FileText } from 'lucide-react';
import { ProjectType } from '@/types';

interface Project {
  id: number;
  name: string;
  slug: string;
  description?: string | null;
  code_source?: string | null;
  automation_framework?: string | null;
  programming_language?: string | null;
  created_at: string;
}

interface ProjectCreateModalProps {
  onClose: () => void;
  onProjectCreated: (project: Project) => void;
}

const isValidSSHUrl = (url: string): boolean => {
  if (!url.trim()) return true; // Allow empty URL

  // SSH URL patterns:
  // **************:username/repo.git
  // **************:username/repo.git
  // ssh://**************/username/repo.git
  const sshPatterns = [
    /^git@[\w.-]+:[\w.-]+\/[\w.-]+\.git$/,
    /^ssh:\/\/git@[\w.-]+\/[\w.-]+\/[\w.-]+\.git$/
  ];

  return sshPatterns.some(pattern => pattern.test(url));
};

export function ProjectCreateModal({ onClose, onProjectCreated }: Readonly<ProjectCreateModalProps>) {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    code_source: '',
    automation_framework: '',
    programming_language: '',
    project_type: ProjectType.GENERATE_FROM_REQUIREMENTS,
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === 'project_type' ? parseInt(value) : value
    }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleNext = () => {
    if (step === 1 && !formData.name.trim()) {
      setError('Project name is required');
      return;
    }
    setError('');
    setStep(step + 1);
  };

  const handleBack = () => {
    setError('');
    setStep(step - 1);
  };

  const handleSubmit = async () => {
    // Validate SSH URL if provided
    if (formData.code_source.trim() && !isValidSSHUrl(formData.code_source)) {
      setError('Please enter a valid SSH Git repository URL (e.g., **************:username/repo.git)');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const projectData = {
        ...formData,
        automation_framework: formData.automation_framework || undefined,
        programming_language: formData.programming_language || undefined,
      };

      const newProject = await apiClient.createProject(projectData);
      onProjectCreated(newProject);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create project');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <BadgePlus className="h-5 w-5" />
            <span>Create New Project</span>
          </DialogTitle>
          <DialogDescription>
            Step {step} of 3: Set up your new IntelliTest project
          </DialogDescription>
        </DialogHeader>

        <DialogBody>
          <div className="space-y-6">
            {/* Step 1: Basic Information */}
            {step === 1 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Project Name *</Label>
                  <Input
                    id="name"
                    placeholder="Enter project name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Describe your project (optional)"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                  />
                </div>
              </div>
            )}

            {/* Step 2: Project Type Selection */}
            {step === 2 && (
              <div className="space-y-4">
                <div className="space-y-3">
                  <Label className="text-base font-medium">Project Type</Label>
                  <p className="text-sm text-gray-600">Choose how you want to create your test cases:</p>

                  <div className="space-y-3">
                    <div
                      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                        formData.project_type === ProjectType.GENERATE_FROM_REQUIREMENTS
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleInputChange('project_type', ProjectType.GENERATE_FROM_REQUIREMENTS.toString())}
                    >
                      <div className="flex items-start space-x-3">
                        <input
                          type="radio"
                          name="project_type"
                          value={ProjectType.GENERATE_FROM_REQUIREMENTS}
                          checked={formData.project_type === ProjectType.GENERATE_FROM_REQUIREMENTS}
                          onChange={() => handleInputChange('project_type', ProjectType.GENERATE_FROM_REQUIREMENTS.toString())}
                          className="mt-1"
                        />
                        <div>
                          <div className="flex items-center space-x-2">
                            <FileText className="h-5 w-5 text-blue-600" />
                            <h3 className="font-medium">Generate from Requirements</h3>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            Define requirements first, then use AI to generate test cases automatically.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div
                      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                        formData.project_type === ProjectType.IMPORT_TEST_CASES
                          ? 'border-green-500 bg-green-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleInputChange('project_type', ProjectType.IMPORT_TEST_CASES.toString())}
                    >
                      <div className="flex items-start space-x-3">
                        <input
                          type="radio"
                          name="project_type"
                          value={ProjectType.IMPORT_TEST_CASES}
                          checked={formData.project_type === ProjectType.IMPORT_TEST_CASES}
                          onChange={() => handleInputChange('project_type', ProjectType.IMPORT_TEST_CASES.toString())}
                          className="mt-1"
                        />
                        <div>
                          <div className="flex items-center space-x-2">
                            <FileSpreadsheet className="h-5 w-5 text-green-600" />
                            <h3 className="font-medium">Import Test Cases</h3>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            Upload existing test cases from Excel and auto-generate requirements.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Technical Configuration and Code Source */}
            {step === 3 && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Automation Framework</Label>
                    <Select
                      value={formData.automation_framework || "none"}
                      onValueChange={(value) => handleInputChange('automation_framework', value === "none" ? "" : value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select framework (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        <SelectItem value="selenium">Selenium</SelectItem>
                        <SelectItem value="playwright">Playwright</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Programming Language</Label>
                    <Select
                      value={formData.programming_language || "none"}
                      onValueChange={(value) => handleInputChange('programming_language', value === "none" ? "" : value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select language (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        <SelectItem value="python">Python</SelectItem>
                        <SelectItem value="javascript">JavaScript</SelectItem>
                        <SelectItem value="java">Java</SelectItem>
                        <SelectItem value="csharp">C#</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="code_source">SSH Git Repository URL (Optional)</Label>
                  <Input
                    id="code_source"
                    placeholder="**************:username/repository.git"
                    value={formData.code_source}
                    onChange={(e) => handleInputChange('code_source', e.target.value)}
                  />
                </div>
              </div>
            )}

            {error && (
              <div className="text-red-600 text-sm">
                {error}
              </div>
            )}
          </div>
        </DialogBody>

        <DialogFooter>
          <div className="w-full flex justify-between">
            <div>
              {step > 1 && (
                <Button variant="outline" onClick={handleBack}>
                  Back
                </Button>
              )}
            </div>
            
            <div className="flex space-x-2">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              
              {step < 3 ? (
                <Button onClick={handleNext}>
                  Next
                </Button>
              ) : (
                <Button onClick={handleSubmit} disabled={loading}>
                  {loading ? 'Creating...' : 'Create Project'}
                </Button>
              )}
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
