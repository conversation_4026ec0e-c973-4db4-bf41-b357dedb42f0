'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { apiClient } from '@/lib/api';

export function TestApiConnection() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testConnection = async () => {
    setLoading(true);
    setResult('Testing...');
    
    try {
      // Test basic API connection
      const response = await fetch('http://localhost:8000/api/v1/auth/me', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      setResult(`Status: ${response.status}, Response: ${JSON.stringify(data, null, 2)}`);
      
    } catch (error) {
      setResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const testFileUpload = async () => {
    setLoading(true);
    setResult('Testing file upload...');
    
    try {
      // Create a simple test file
      const testContent = 'Test,Data\nValue1,Value2';
      const blob = new Blob([testContent], { type: 'text/csv' });
      const file = new File([blob], 'test.csv', { type: 'text/csv' });
      
      const result = await apiClient.uploadFile(file);
      setResult(`Upload successful: ${JSON.stringify(result, null, 2)}`);
      
    } catch (error) {
      setResult(`Upload error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg space-y-4">
      <h3 className="font-medium">API Connection Test</h3>
      
      <div className="space-x-2">
        <Button onClick={testConnection} disabled={loading}>
          Test API Connection
        </Button>
        <Button onClick={testFileUpload} disabled={loading}>
          Test File Upload
        </Button>
      </div>
      
      {result && (
        <div className="bg-gray-100 p-3 rounded text-sm">
          <pre>{result}</pre>
        </div>
      )}
    </div>
  );
}
