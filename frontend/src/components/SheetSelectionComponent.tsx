'use client';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FileSpreadsheet } from 'lucide-react';
import { FileUploadResponse } from '@/types';

interface SheetSelectionComponentProps {
  fileData: FileUploadResponse;
  selectedSheets: string[];
  onSheetsSelected: (sheets: string[]) => void;
}

export function SheetSelectionComponent({
  fileData,
  selectedSheets,
  onSheetsSelected
}: Readonly<SheetSelectionComponentProps>) {

  const handleSheetToggle = (sheetName: string) => {
    const newSelected = selectedSheets.includes(sheetName)
      ? selectedSheets.filter(s => s !== sheetName)
      : [...selectedSheets, sheetName];
    onSheetsSelected(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedSheets.length === fileData.sheets.length) {
      onSheetsSelected([]);
    } else {
      onSheetsSelected([...fileData.sheets]);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Select Sheets to Import</h3>
          <p className="text-sm text-gray-600">
            Choose which sheets from your file you want to import. Each sheet will be treated as a module.
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleSelectAll}
        >
          {selectedSheets.length === fileData.sheets.length ? 'Deselect All' : 'Select All'}
        </Button>
      </div>

      <div className="space-y-4">
        {fileData.sheets.map((sheetName) => {
          const isSelected = selectedSheets.includes(sheetName);

          return (
            <Card key={sheetName} className={`transition-colors ${isSelected ? 'ring-2 ring-blue-500 py-3' : 'py-3'}`}>
              <CardHeader className="mt-1">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      checked={isSelected}
                      onCheckedChange={() => handleSheetToggle(sheetName)}
                    />
                    <FileSpreadsheet className="h-5 w-5 text-green-600" />
                    <div>
                      <CardTitle className="text-base">{sheetName}</CardTitle>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {isSelected && (
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                        Selected
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
            </Card>
          );
        })}
      </div>

      {selectedSheets.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-blue-900">
                {selectedSheets.length} sheet{selectedSheets.length !== 1 ? 's' : ''} selected
              </p>
              <p className="text-sm text-blue-700">
                Selected: {selectedSheets.join(', ')}
              </p>
            </div>
          </div>
        </div>
      )}


    </div>
  );
}
