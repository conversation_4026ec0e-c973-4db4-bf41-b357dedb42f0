'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Upload, FileSpreadsheet, X, AlertCircle, CheckCircle } from 'lucide-react';
import { FileUploadResponse } from '@/types';
import { apiClient } from '@/lib/api';

interface FileUploadComponentProps {
  onFileUploaded: (response: FileUploadResponse) => void;
  onError: (error: string) => void;
  disabled?: boolean;
}

export function FileUploadComponent({ onFileUploaded, onError, disabled = false }: FileUploadComponentProps) {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFile, setUploadedFile] = useState<FileUploadResponse | null>(null);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;
    
    const file = acceptedFiles[0];
    
    // Validate file type
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel' // .xls
    ];
    
    if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
      onError('Please upload an Excel (.xlsx, .xls) file');
      return;
    }
    
    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      onError('File size must be less than 10MB');
      return;
    }
    
    setUploading(true);
    setUploadProgress(0);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const uploadResult = await apiClient.uploadFile(file);

      clearInterval(progressInterval);
      setUploadProgress(100);

      setUploadedFile(uploadResult);
      onFileUploaded(uploadResult);
      
    } catch (error) {
      let errorMessage = 'Upload failed';

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Provide more user-friendly error messages
      if (errorMessage.includes('413')) {
        errorMessage = 'File is too large. Please upload a file smaller than 10MB.';
      } else if (errorMessage.includes('415')) {
        errorMessage = 'Unsupported file type. Please upload an Excel (.xlsx, .xls) file.';
      } else if (errorMessage.includes('Network Error') || errorMessage.includes('fetch')) {
        errorMessage = 'Network error. Please check your connection and try again.';
      }

      onError(errorMessage);
    } finally {
      setUploading(false);
      setTimeout(() => setUploadProgress(0), 1000);
    }
  }, [onFileUploaded, onError]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    multiple: false,
    disabled: disabled || uploading
  });

  const handleRemoveFile = () => {
    setUploadedFile(null);
    setUploadProgress(0);
  };

  if (uploadedFile) {
    return (
      <div className="border-2 border-green-200 border-dashed rounded-lg p-6 bg-green-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <CheckCircle className="h-8 w-8 text-green-600" />
            <div>
              <h3 className="font-medium text-green-900">{uploadedFile.filename}</h3>
              <p className="text-sm text-green-700">
                {uploadedFile.sheets.length} sheet{uploadedFile.sheets.length !== 1 ? 's' : ''} • {uploadedFile.total_rows} rows
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRemoveFile}
            className="text-green-700 hover:text-green-900"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
          isDragActive
            ? 'border-blue-400 bg-blue-50'
            : uploading
            ? 'border-gray-300 bg-gray-50 cursor-not-allowed'
            : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
        }`}
      >
        <input {...getInputProps()} />
        
        {uploading ? (
          <div className="space-y-4">
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Uploading file...</p>
              <Progress value={uploadProgress} className="w-full max-w-xs mx-auto" />
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex justify-center">
              {isDragActive ? (
                <FileSpreadsheet className="h-12 w-12 text-blue-600" />
              ) : (
                <Upload className="h-12 w-12 text-gray-400" />
              )}
            </div>
            
            <div className="space-y-2">
              <p className="text-lg font-medium text-gray-900">
                {isDragActive ? 'Drop your file here' : 'Upload Excel file'}
              </p>
              <p className="text-sm text-gray-600">
                Drag and drop your file here, or click to browse
              </p>
              <p className="text-xs text-gray-500">
                Supports .xlsx and .xls files (max 10MB)
              </p>
            </div>
            
            <Button variant="outline" className="mt-4">
              Choose File
            </Button>
          </div>
        )}
      </div>
      
      {/* File format help */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-2">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div className="text-sm">
            <p className="font-medium text-blue-900 mb-1">File Format Requirements:</p>
            <ul className="text-blue-800 space-y-1 text-xs">
              <li>• Each sheet will be treated as a module</li>
              <li>• Include columns for: Test Steps, Expected Results</li>
              <li>• Optional columns: Test Title, Submodule, Notes</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
