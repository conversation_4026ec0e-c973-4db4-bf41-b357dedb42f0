'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { apiClient } from '@/lib/api';

import { Download, FileCode, Loader2, CheckCircle, XCircle } from 'lucide-react';

interface TestCase {
  id: number;
  custom_id: string;
  title: string;
  steps?: string;
  expected_result?: string;
  notes?: string;
  requirement_id?: number;
  created_at?: string;
  updated_at?: string;
}

interface CrawlData {
  [url: string]: {
    url: string;
    elements: Array<{
      tag: string;
      selector: string;
      text: string;
    }>;
  };
}

interface Project {
  id: number;
  name: string;
  automation_framework?: string | null;
  programming_language?: string | null;
  code_source?: string | null;
}

interface CodeGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project;
  requirementId: number;
  testCases: TestCase[];
  crawlData: CrawlData;
}

interface GenerationProgress {
  type: string;
  status?: string;
  message?: string;
  progress?: number;
  generated_files_count?: number;
  error_message?: string;
  details?: string; // For validation errors
  data?: {
    code_chunk?: string;
    chunk?: string;
    path?: string;
    action?: string;
    content?: string;
    explanation?: string;
    explanation_chunk?: string;
    details?: string; // For validation errors in data
    [key: string]: unknown;
  };
}

export default function CodeGenerationModal({
  isOpen,
  onClose,
  project,
  requirementId,
  testCases,
  crawlData
}: Readonly<CodeGenerationModalProps>) {
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [status, setStatus] = useState<'idle' | 'starting' | 'generating' | 'completed' | 'failed'>('idle');
  const [explanation, setExplanation] = useState('');
  const [generatedCode, setGeneratedCode] = useState('');
  const [agenticInsights, setAgenticInsights] = useState<{status: string; timestamp: Date} | null>(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [sshPrivateKey, setSshPrivateKey] = useState('');
  const [sshPassphrase, setSshPassphrase] = useState('');
  const [showSshFields, setShowSshFields] = useState(false);

  // Check if SSH fields should be shown based on git URL
  useEffect(() => {
    const gitUrl = project.code_source;
    if (gitUrl && (gitUrl.startsWith('git@') || gitUrl.startsWith('ssh://'))) {
      setShowSshFields(true);
    }
  }, [project.code_source]);

  // Chat states (simplified - no code review)
  const [chatMessages, setChatMessages] = useState<Array<{type: 'system' | 'user', content: string, timestamp: Date, isStreaming?: boolean}>>([]);
  // TODO: Hide chat input and send button for now
  // const [userInput, setUserInput] = useState('');
  // const [isWaitingForResponse, setIsWaitingForResponse] = useState(false);

  const eventSourceRef = useRef<EventSource | null>(null);

  // Enhanced markdown renderer with consistent text-sm sizing and proper formatting
  const renderMarkdown = (text: string): string => {
    if (!text) return '';

    let rendered = text;

    // Remove lines with only equals signs (========)
    rendered = rendered.replace(/^=+\s*$/gm, '');

    // Handle code blocks (```language\ncode\n```) - remove language and render as code with text wrapping
    rendered = rendered.replace(/```(?:\w+)?\n([\s\S]*?)\n```/g, (_, code) => {
      return `<pre class="bg-gray-100 p-2 rounded-md my-2 whitespace-pre-wrap break-words"><code class="text-xs font-mono">${code.trim()}</code></pre>`;
    });

    // Handle inline code (`code`) - render with background, uniform text-sm
    rendered = rendered.replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono">$1</code>');

    // Handle bold text (**text** or *text*) - uniform text-sm, keep bold formatting
    rendered = rendered.replace(/\*\*([^*]+)\*\*/g, '<strong class="text-xs font-semibold">$1</strong>');
    rendered = rendered.replace(/\*([^*]+)\*/g, '<strong class="text-xs font-semibold">$1</strong>');

    // Handle headers (## Header) - uniform text-sm with bold
    rendered = rendered.replace(/^### (.+)$/gm, '<div class="text-xs font-semibold mt-3 mb-1">✅ $1</div>');
    rendered = rendered.replace(/^## (.+)$/gm, '<div class="text-xs font-semibold mt-3 mb-1">$1</div>');
    rendered = rendered.replace(/^# (.+)$/gm, '<div class="text-xs font-semibold mt-3 mb-1">$1</div>');

    // Handle bullet points (- item or * item) - uniform text-sm
    rendered = rendered.replace(/^[ \t]*[-*]\s+(.+)$/gm, '<div class="ml-3 text-xs mb-1">• $1</div>');

    // Handle numbered lists (1. item) - uniform text-sm
    rendered = rendered.replace(/^[ \t]*(\d+)\.\s+(.+)$/gm, '<div class="ml-3 text-xs mb-1">$1. $2</div>');

    // Handle line breaks - preserve paragraph spacing
    rendered = rendered.replace(/\n\n/g, '<div class="mb-2"></div>');

    // Step 1: Replace \n inside <code> blocks with <br>
    const codeBlocks: string[] = [];
    let placeholderIndex = 0;

    const renderedWithoutCode = rendered.replace(
      /<code\b[^>]*>([\s\S]*?)<\/code>/g,
      (match) => {
        codeBlocks.push(match.replace(/\n/g, '<br>')); // Replace \n with <br> inside code
        return `__CODE_BLOCK_${placeholderIndex++}__`; // Temporary placeholder
      }
    );

    // Step 2: Replace \n outside code blocks with ''
    const renderedCleaned = renderedWithoutCode.replace(/\n/g, '');

    // Step 3: Restore the <code> blocks
    const finalRendered = renderedCleaned.replace(/__CODE_BLOCK_(\d+)__/g, (_, i) => codeBlocks[i]);

    // Final result
    rendered = finalRendered;

    return rendered;
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'idle':
        return <FileCode className="h-6 w-6 text-white" />;
      case 'starting':
      case 'generating':
        return <Loader2 className="h-6 w-6 text-white animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-6 w-6 text-white" />;
      case 'failed':
        return <XCircle className="h-6 w-6 text-white" />;
      default:
        return <FileCode className="h-6 w-6 text-white" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'idle':
        return 'Ready to Generate';
      case 'starting':
        return 'Starting Generation...';
      case 'generating':
        return 'Generating Code...';
      case 'completed':
        return 'Generation Complete!';
      case 'failed':
        return 'Generation Failed';
      default:
        return 'Code Generation';
    }
  };



  // Handle browser tab close/refresh - close stream gracefully and cleanup
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      // Cleanup project repository on browser close
      if (sessionId) {
        try {
          // Use navigator.sendBeacon for reliable cleanup on page unload
          const cleanupUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'}/code-generation/cleanup/${sessionId}`;
          const token = localStorage.getItem('token');

          if (token) {
            // Create a FormData object with the token for sendBeacon
            const formData = new FormData();
            formData.append('token', token);
            formData.append('method', 'DELETE');

            // Try sendBeacon first (most reliable for page unload)
            const beaconSent = navigator.sendBeacon(cleanupUrl, formData);

            if (!beaconSent) {
              // Fallback to synchronous fetch if sendBeacon fails
              fetch(cleanupUrl, {
                method: 'DELETE',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json'
                },
                keepalive: true
              }).catch(() => {
                // Ignore errors during cleanup on page unload
              });
            }
          }
        } catch (error) {
          // Ignore errors during cleanup on page unload
          console.warn('Cleanup on page unload failed:', error);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [sessionId]);

  const resetModal = useCallback(async () => {
    // Cleanup project repository before resetting
    if (sessionId) {
      try {
        await apiClient.cleanupCodeGeneration(sessionId);
        console.log('Project repository cleaned up successfully');
      } catch (error) {
        console.error('Error cleaning up project repository:', error);
        // Continue with reset even if cleanup fails
      }
    }

    setSessionId(null);
    setStatus('idle');
    setExplanation('');
    setGeneratedCode('');
    setIsStreaming(false);
    setChatMessages([]);
    // TODO: Reset chat input when re-enabled
    // setUserInput('');
    // setIsWaitingForResponse(false);

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }
  }, [sessionId]);

  const downloadAndClose = async () => {
    if (!sessionId) return;

    try {
      const blob = await apiClient.downloadGeneratedCode(sessionId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `${project.name}_automation_tests.zip`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // Cleanup project repository after download
      try {
        await apiClient.cleanupCodeGeneration(sessionId);
        console.log('Project repository cleaned up after download');
      } catch (cleanupError) {
        console.error('Error cleaning up project repository after download:', cleanupError);
        // Continue with modal close even if cleanup fails
      }

      // Close modal after download and cleanup
      onClose();
    } catch (error) {
      console.error('Error downloading code:', error);
      setChatMessages(prev => [...prev, {
        type: 'system',
        content: `❌ **Download Error:** ${error instanceof Error ? error.message : 'Failed to download code'}`,
        timestamp: new Date()
      }]);
    }
  };

  // Reset modal state when closed
  useEffect(() => {
    if (!isOpen) {
      resetModal();
    }
  }, [isOpen, resetModal]);

  // TODO: Re-implement handleUserMessage when chat functionality is needed
  // const handleUserMessage = async () => {
  //   // Chat functionality removed for simplified workflow
  // };

  const startCodeGeneration = async () => {
    try {
      setStatus('starting');
      
      // Initialize chat with project summary
      setChatMessages([
        {
          type: 'system',
          content: `🚀 **Starting Code Generation**

**Project Overview:**
1. **Framework:** ${project.automation_framework || 'Selenium'}
2. **Language:** ${project.programming_language || 'Python'}
3. **Test Cases:** ${testCases.length} ready to implement
4. **Pages Crawled:** ${Object.keys(crawlData).length} with elements extracted

✅ Initializing code generation environment...`,
          timestamp: new Date()
        }
      ]);

      const data = await apiClient.startCodeGeneration({
        requirement_id: requirementId,
        project_id: project.id,
        automation_framework: project.automation_framework || 'selenium',
        programming_language: project.programming_language || 'python',
        git_url: project.code_source || undefined,
        page_elements: crawlData,
        ssh_private_key: sshPrivateKey || undefined,
        ssh_passphrase: sshPassphrase || undefined,
      });
      setSessionId(data.session_id);
      setStatus('generating');
      setIsStreaming(true);

      // Start streaming immediately since backend now has a delay
      startEventStream(data.session_id);
    } catch (error) {
      console.error('Error starting code generation:', error);
      setStatus('failed');

      // Check if it's an SSH-related error
      if (error instanceof Error && error.message.includes('SSH')) {
        setShowSshFields(true);
      }

      setChatMessages(prev => [...prev, {
        type: 'system',
        content: `❌ **Error:** ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
        timestamp: new Date()
      }]);
    }
  };

  const startEventStream = (sessionId: string) => {
    const token = localStorage.getItem('token');
    if (!token) {
      setStatus('failed');
      setChatMessages(prev => [...prev, {
        type: 'system',
        content: `❌ **Authentication Error:** No authentication token found. Please refresh and try again.`,
        timestamp: new Date()
      }]);
      return;
    }

    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';
    const streamUrl = `${API_BASE_URL}/code-generation/stream/${sessionId}?token=${encodeURIComponent(token)}`;

    const eventSource = new EventSource(streamUrl);
    eventSourceRef.current = eventSource;

    eventSource.onopen = () => {
      // Connection opened successfully
    };

    eventSource.onmessage = (event) => {
      try {
        const data: GenerationProgress = JSON.parse(event.data);

        handleProgressUpdate(data);

      } catch (error) {
        console.debug('Error parsing SSE data:', error); //to avoid console spam used debug instead of error
      }
    };

    eventSource.onerror = () => {
      // EventSource error occurred
      console.debug('EventSource error occurred, readyState:', eventSource.readyState, 'status:', status);

      // If generation is completed, don't show error - this is expected stream closure
      if (status === 'completed' || status === 'idle') {
        console.debug('EventSource closed after completion - this is expected');
        eventSource.close();
        setIsStreaming(false);
        return;
      }

      // EventSource readyState 2 means CLOSED, which is normal when stream ends
      // Only show error if it's an unexpected closure AND we haven't received substantial content
      if (eventSource.readyState === EventSource.CONNECTING || eventSource.readyState === EventSource.OPEN) {
        // Only show error if we haven't received substantial content and status isn't completed
        if (explanation.length < 100) {
          // Add error message to chat
          setChatMessages(prev => [...prev, {
            type: 'system',
            content: `❌ **Connection Error:** Lost connection to code generation stream. Please try again.`,
            timestamp: new Date()
          }]);
          setStatus('failed');
        } else {
          console.log('EventSource closed after successful content delivery');
        }
      } else {
        console.log('EventSource closed normally');
      }

      eventSource.close();
      setIsStreaming(false);
    };

    // Handle when the stream closes naturally
    eventSource.addEventListener('close', () => {
      console.log('EventSource closed naturally');
      setIsStreaming(false);
    });
  };

  const handleProgressUpdate = (data: GenerationProgress) => {
    // Handle completion first (highest priority)
    if (data.type === 'complete' || data.status === 'completed') {
      setStatus('completed');
      return;
    }

    // Handle errors
    if (data.type === 'error' || data.status === 'failed') {
      setStatus('failed');
      const errorMessage = data.error_message || data.message || 'Code generation failed';
      
      // Add error message to chat
      setChatMessages(prev => [...prev, {
        type: 'system',
        content: `❌ **Error:** ${errorMessage}`,
        timestamp: new Date()
      }]);
      
      return;
    }

    // Handle streaming progress updates
    switch (data.type) {
      case 'status':
        // Add status updates to chat (but skip explanation-related messages)
        if (data.message && !data.message.toLowerCase().includes('explanation')) {
          setChatMessages(prev => [...prev, {
            type: 'system',
            content: `📋 ${data.message}`,
            timestamp: new Date()
          }]);

          // Capture agentic intelligence insights
          if (data.message.includes('🧠') || data.message.includes('🎯')) {
            setAgenticInsights({
              status: data.message,
              timestamp: new Date()
            });
          }
        }
        break;

      case 'code_chunk':
        if (data.data && typeof data.data === 'object') {
          const chunkData = data.data as {
            code_chunk?: string;
            chunk?: string;
            path?: string;
            action?: string;
            content?: string;
          };

          // Handle both code_chunk and chunk properties for backward compatibility
          const chunk = chunkData.code_chunk || chunkData.chunk;
          const action = chunkData.action;

          console.log('Processing code_chunk:', { chunk: chunk?.substring(0, 50) + '...', action, path: chunkData.path });

          if (chunk) {
            if (action === 'clear' || action === 'clear_and_start') {
              // Clear the panel and start with this chunk
              console.log('Clearing and starting with chunk');
              setGeneratedCode(chunk);
            } else {
              // Append the chunk as normal
              setGeneratedCode(prev => {
                const newCode = prev + chunk;
                return newCode;
              });
            }
          }
        }
        break;

      case 'explanation_chunk':
        if (data.data && typeof data.data === 'object') {
          const chunkData = data.data as { explanation_chunk?: string };
          if (chunkData.explanation_chunk) {
            setExplanation(prev => {
              const newExplanation = prev + chunkData.explanation_chunk;

              // Update chat messages with the new explanation
              setChatMessages(chatPrev => {
                const newMessages = [...chatPrev];
                // Look for existing explanation message (starts with 📖)
                const explanationIndex = newMessages.findIndex(msg =>
                  msg.type === 'system' && msg.content.startsWith('📖 **Code Explanation:**')
                );

                const explanationContent = `📖 **Code Explanation:**\n\n${newExplanation}`;

                if (explanationIndex >= 0) {
                  // Update existing explanation message
                  newMessages[explanationIndex] = {
                    ...newMessages[explanationIndex],
                    content: explanationContent
                  };
                } else {
                  // Add new explanation message
                  newMessages.push({
                    type: 'system',
                    content: explanationContent,
                    timestamp: new Date()
                  });
                }

                return newMessages;
              });

              return newExplanation;
            });
          }
        }
        break;

      case 'explanation_complete':
        // Explanation is complete - no need to add content again since it was already added during streaming
        console.log('Explanation streaming completed');

        // Only add completion message if no explanation was streamed
        if (!explanation || explanation.trim() === '') {
          setChatMessages(prev => [...prev, {
            type: 'system',
            content: `🎉 **Code Generation Complete!**

✅ Your automation test suite is ready for download.

🚀 **Your complete test suite is ready!** Click "Download and Close" to get your files.`,
            timestamp: new Date()
          }]);
        }

        // Mark generation as completed
        setStatus('completed');
        break;

      case 'generation_complete':
        // Stream will close automatically after 2 minutes
        console.log('Generation complete, stream will close automatically in 2 minutes');
        setStatus('completed');
        // Keep streaming active for 2 minutes to allow reconnection if needed
        break;

      case 'stream_end':
        // Stream is ending gracefully
        console.log('Stream ended gracefully');
        setIsStreaming(false);
        // Don't change status or show error - this is expected
        break;

      case 'detailed_analysis':
        if (data.data) {
          console.log('Received detailed analysis:', data.data);

          // Add comprehensive analysis summary to chat
          const analysisData = data.data as Record<string, unknown>;
          const existingTestCases = (analysisData.existing_test_cases as unknown[]) || [];
          const unimplementedTestCases = (analysisData.unimplemented_test_cases as unknown[]) || [];
          const strategy = (analysisData.generation_strategy as Record<string, unknown>) || {};
          const implementationSummary = (analysisData.implementation_summary as Record<string, unknown>) || {};

          let analysisMessage = `📊 **Incremental Code Analysis Complete:**\n\n`;

          // Implementation summary
          if (implementationSummary.total_test_cases) {
            analysisMessage += `📈 **Test Case Status:**\n`;
            analysisMessage += `• Total Test Cases: ${String(implementationSummary.total_test_cases)}\n`;
            analysisMessage += `• Already Implemented: ${String(implementationSummary.implemented_count)}\n`;
            analysisMessage += `• Need Implementation: ${String(implementationSummary.unimplemented_count)}\n\n`;
          }

          // Show existing implemented test cases
          if (existingTestCases.length > 0) {
            analysisMessage += `✅ **Already Implemented (${existingTestCases.length}):**\n`;
            existingTestCases.slice(0, 3).forEach((tc: unknown, index: number) => {
              const testCase = tc as Record<string, unknown>;
              const tcId = (typeof testCase.test_case_id === 'string' || typeof testCase.test_case_id === 'number')
                ? String(testCase.test_case_id) : 'Unknown ID';
              const methodName = (typeof testCase.method_name === 'string')
                ? testCase.method_name : 'Unknown Method';
              analysisMessage += `${index + 1}. ${tcId}: ${methodName}\n`;
            });
            if (existingTestCases.length > 3) {
              analysisMessage += `... and ${existingTestCases.length - 3} more\n`;
            }
            analysisMessage += `\n`;
          }

          // Show test cases that need implementation
          if (unimplementedTestCases.length > 0) {
            analysisMessage += `🔧 **Will Implement (${unimplementedTestCases.length}):**\n`;
            unimplementedTestCases.slice(0, 5).forEach((tc: unknown, index: number) => {
              const testCase = tc as Record<string, unknown>;
              const tcId = (typeof testCase.custom_id === 'string' || typeof testCase.custom_id === 'number')
                ? String(testCase.custom_id) : `TC-${String(index + 1).padStart(3, '0')}`;
              const title = (typeof testCase.title === 'string')
                ? testCase.title : 'Unknown Test';
              analysisMessage += `${index + 1}. ${tcId}: ${title}\n`;
            });
            if (unimplementedTestCases.length > 5) {
              analysisMessage += `... and ${unimplementedTestCases.length - 5} more\n`;
            }
            analysisMessage += `\n`;
          }

          // Generation strategy
          if (strategy.strategy && typeof strategy.strategy === 'string') {
            analysisMessage += `🎯 **Strategy:** ${strategy.strategy}\n\n`;
            if (strategy.target_file && typeof strategy.target_file === 'string') {
              analysisMessage += `📂 **Target File:** ${strategy.target_file}\n\n`;
            }
            if (strategy.reason && typeof strategy.reason === 'string') {
              analysisMessage += `📝 **Reason:** ${strategy.reason}`;
            }
          }

          setChatMessages(prev => [...prev, {
            type: 'system',
            content: analysisMessage,
            timestamp: new Date()
          }]);
        }
        break;

      case 'structure':
        // Project structure information - can be ignored for UI
        break;

      case 'file_start':
        // File generation started - can be ignored for UI
        break;

      case 'file_complete':
        // Individual file completed - can be ignored for UI
        break;

      case 'file':
        // File information - can be ignored for UI
        break;

      case 'validation_warning':
        // Show validation warning but continue generation
        if (data.message) {
          setChatMessages(prev => [...prev, {
            type: 'system',
            content: `⚠️ **Validation Warning:** ${data.message}`,
            timestamp: new Date()
          }]);
        }
        break;

      case 'validation_error': {
        // Show validation error and stop generation
        setStatus('failed');
        const validationErrorMessage = data.details || data.message || 'Repository validation failed';

        setChatMessages(prev => [...prev, {
          type: 'system',
          content: `❌ **Validation Error:** ${validationErrorMessage}`,
          timestamp: new Date()
        }]);

        // Stop streaming
        setIsStreaming(false);
        break;
      }

      default:
        // Only log unhandled types that might be important
        if (!['status', 'keepalive'].includes(data.type)) {
          console.log('Unhandled progress type:', data.type);
        }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="!max-w-[1400px] !max-h-[100vh] overflow-hidden !p-0 !border-0 !bg-transparent !shadow-none [&>button]:hidden">
        <DialogTitle className="sr-only">Code Generation</DialogTitle>
        
        {/* Modern Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getStatusIcon()}
              <div>
                <h2 className="text-xl font-semibold">{getStatusText()}</h2>
                <p className="text-blue-100 text-sm">
                  {project.name} • {project.automation_framework || 'Selenium'} • {project.programming_language || 'Python'}
                </p>
              </div>
            </div>
            <Button
              onClick={onClose}
              variant="ghost"
              className="text-white hover:bg-white/20 h-8 w-8 p-0 rounded-full"
            >
              <XCircle className="h-5 w-5" />
            </Button>
          </div>
        </div>

        <div className="flex h-[calc(100vh-120px)] bg-white rounded-b-lg overflow-hidden">
          {/* Left Panel - Chat Interface */}
          <div className="w-1/3 flex flex-col bg-gray-50 border-r border-gray-200">
            {/* Scrollable Chat Area */}
            <div className="flex-1 overflow-y-auto p-4">
              {/* Agentic Intelligence Insights */}
              {agenticInsights && (
                <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-3 mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                    <h3 className="text-xs font-semibold text-purple-800">🧠 Agentic Intelligence</h3>
                  </div>
                  <div className="text-xs text-purple-700">
                    {agenticInsights.status}
                  </div>
                  {agenticInsights.timestamp && (
                    <div className="text-xs text-purple-500 mt-1">
                      {agenticInsights.timestamp.toLocaleTimeString()}
                    </div>
                  )}
                </div>
              )}

              {/* Chat Messages */}
              <div className="space-y-4" style={{ fontSize: '12px' }}>
                {status === 'idle' && chatMessages.length === 0 && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 text-gray-900 p-3 rounded-lg max-w-[90%]">
                      <div
                        className="whitespace-pre-wrap"
                        style={{ fontSize: '12px' }}
                        dangerouslySetInnerHTML={{
                          __html: renderMarkdown(`🚀 **Ready to Generate Code**

I'll create a complete **${project.automation_framework || 'Selenium'}** automation test suite in **${project.programming_language || 'Python'}** based on your project data.

**What I have:**
1. **${testCases.length} Test Cases** - Ready to implement
2. **${Object.keys(crawlData).length} Pages Crawled** - Elements extracted and analyzed
3. **Framework:** ${project.automation_framework || 'Selenium'}
4. **Language:** ${project.programming_language || 'Python'}

✅ Click **"Generate Code"** to start the magic!`)
                        }}
                      />
                      <div className="text-xs mt-1 text-gray-500">
                        {new Date().toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                )}

                {/* Display chat messages */}
                {chatMessages.map((message, index) => (
                  <div key={`${message.timestamp.getTime()}-${index}`} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[80%] p-3 rounded-lg ${
                      message.type === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}>
                      <div
                        className="whitespace-pre-wrap"
                        style={{ fontSize: '12px' }}
                        dangerouslySetInnerHTML={{
                          __html: renderMarkdown(message.content)
                        }}
                      />
                      <div className={`text-xs mt-1 ${
                        message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        {message.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                ))}

                {/* Loading states removed for simplified workflow */}
              </div>
            </div>

            {/* SSH Configuration Panel (only for idle state and when Git URL is provided) */}
            {status === 'idle' && showSshFields && (
              <div className="p-4 border-t border-gray-200 bg-white">
                <div className="space-y-4">
                  <div>
                    <label htmlFor="ssh-private-key" className="block text-sm font-medium text-gray-700 mb-2">
                      SSH Private Key *
                    </label>
                    <textarea
                      id="ssh-private-key"
                      value={sshPrivateKey}
                      onChange={(e) => setSshPrivateKey(e.target.value)}
                      placeholder="-----BEGIN OPENSSH PRIVATE KEY-----&#10;...&#10;-----END OPENSSH PRIVATE KEY-----"
                      className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md text-sm font-mono resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="ssh-passphrase" className="block text-sm font-medium text-gray-700 mb-2">
                      SSH Passphrase (optional)
                    </label>
                    <input
                      id="ssh-passphrase"
                      type="password"
                      value={sshPassphrase}
                      onChange={(e) => setSshPassphrase(e.target.value)}
                      placeholder="Enter passphrase if your key is encrypted"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Pro Tip Banner (only for idle state and when no Git URL is provided) */}
            {status === 'idle' && !showSshFields && (
              <div className="p-4 border-t border-gray-200 bg-white">
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 p-3 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <div className="flex-shrink-0 w-5 h-5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">💡</span>
                    </div>
                    <div className="flex-1">
                      <p className="text-xs font-medium text-blue-800 mb-1">Important Note</p>
                      <p className="text-xs text-blue-700">
                        Since no Git repository link was provided, the code generation will be <strong>standalone</strong> based on your test cases.
                        No existing codebase will be embedded for context, ensuring a fresh start with best practices incorporated.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* TODO: Chat input and Send button hidden for simplified workflow */}
            {/* Chat functionality will be re-enabled in future updates */}

            {/* Action Buttons */}
            <div className="p-4 border-t border-gray-200 bg-white">
              {status === 'idle' && (
                <Button
                  onClick={startCodeGeneration}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
                  disabled={
                    isStreaming ||
                    !project.automation_framework ||
                    !project.programming_language ||
                    (showSshFields && !sshPrivateKey.trim())
                  }
                >
                  {isStreaming ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Generating...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <FileCode className="h-5 w-5 text-white" />
                      Generate Code
                    </div>
                  )}
                </Button>
              )}

              {status === 'completed' && (
                <Button
                  onClick={downloadAndClose}
                  className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium py-3 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <Download className="h-5 w-5 mr-2" />
                  Download and Close
                </Button>
              )}

              {status === 'failed' && (
                <Button
                  onClick={startCodeGeneration}
                  variant="outline"
                  className="w-full border-red-300 text-red-600 hover:bg-red-50 font-medium py-3 rounded-lg transition-all duration-200"
                >
                  Try Again
                </Button>
              )}
            </div>
          </div>

          {/* Right Panel - Generated Code */}
          <div className="flex-1 flex flex-col bg-gray-900">
            {/* Code Panel Header */}
            <div className="flex items-center justify-between p-2 border-b border-gray-700 bg-gray-800">
              <div className="flex gap-1.5">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <div className="text-gray-400 text-sm font-mono flex items-center gap-2">
                Generated Code
              </div>
              <div className="w-16"></div>
            </div>

            {/* Code Content */}
            <div className="flex-1 overflow-hidden">
              {generatedCode ? (
                <pre className="h-full overflow-auto p-4 text-sm font-mono text-green-400 bg-gray-900 whitespace-pre-wrap" style={{ fontFamily: 'Fira Code, monospace', fontSize: '12px' }}>
                  <code>{generatedCode}</code>
                </pre>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-400">
                  <div className="text-center space-y-4">
                    {isStreaming ? (
                      <>
                        <Loader2 className="w-16 h-16 mx-auto animate-spin opacity-50" />
                        <div className="text-lg font-medium">Generating Code...</div>
                        <div className="text-sm">Code will stream here in real-time</div>
                      </>
                    ) : (
                      <>
                        <FileCode className="w-16 h-16 mx-auto opacity-50" />
                        <div className="text-lg font-medium">No Code Generated Yet</div>
                        <div className="text-sm">Generated code will appear here</div>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
