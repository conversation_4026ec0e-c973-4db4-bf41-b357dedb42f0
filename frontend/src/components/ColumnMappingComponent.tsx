'use client';

import { useState, useEffect } from 'react';

import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Settings } from 'lucide-react';
import { ColumnMapping } from '@/types';

interface ColumnMappingComponentProps {
  fileId: string;
  selectedSheets: string[];
  columnMapping: ColumnMapping;
  onMappingChange: (mapping: ColumnMapping) => void;
}

export function ColumnMappingComponent({
  selectedSheets,
  columnMapping,
  onMappingChange
}: Readonly<Omit<Readonly<ColumnMappingComponentProps>, 'fileId'>>) {
  // Generate fixed column options A-Z (27 options including None)
  const availableColumns = ['None', ...Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i))];

  const handleMappingChange = (field: keyof ColumnMapping, value: string | number | undefined) => {
    // Handle None selection
    const actualValue = value === 'None' ? undefined : value;

    onMappingChange({
      ...columnMapping,
      [field]: actualValue
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium flex items-center space-x-2">
          <Settings className="h-5 w-5" />
          <span>Configure Column Mapping</span>
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          Map your Excel columns to test case fields. This mapping will be applied to all selected sheets.
        </p>
      </div>

      {/* Selected Sheets Summary */}
      <Card className="gap-3">
        <CardHeader>
          <CardTitle className="text-base">Selected Sheets</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {selectedSheets.map(sheet => (
              <Badge key={sheet} variant="secondary">
                {sheet}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Column Mapping Form */}
      <Card className="gap-3">
        <CardHeader>
          <CardTitle className="text-base">Column Mapping</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Row 1: Submodule Column and Test Title Column */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Submodule Column (Optional)</Label>
              <Select
                value={columnMapping.submodule_column || 'None'}
                onValueChange={(value) => handleMappingChange('submodule_column', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select submodule column" />
                </SelectTrigger>
                <SelectContent>
                  {availableColumns.map(column => (
                    <SelectItem key={column} value={column}>
                      {column}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Test Title Column (Optional)</Label>
              <Select
                value={columnMapping.title_column || 'None'}
                onValueChange={(value) => handleMappingChange('title_column', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select title column" />
                </SelectTrigger>
                <SelectContent>
                  {availableColumns.map(column => (
                    <SelectItem key={column} value={column}>
                      {column}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Row 2: Test Steps Column and Expected Results Column */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center space-x-1">
                <span>Test Steps Column</span>
                <span className="text-red-500">*</span>
              </Label>
              <Select
                value={columnMapping.steps_column || 'None'}
                onValueChange={(value) => handleMappingChange('steps_column', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select column for test steps" />
                </SelectTrigger>
                <SelectContent>
                  {availableColumns.map(column => (
                    <SelectItem key={column} value={column}>
                      {column}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Expected Results Column (Optional)</Label>
              <Select
                value={columnMapping.expected_result_column || 'None'}
                onValueChange={(value) => handleMappingChange('expected_result_column', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select column for expected results" />
                </SelectTrigger>
                <SelectContent>
                  {availableColumns.map(column => (
                    <SelectItem key={column} value={column}>
                      {column}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Row 3: Starting Row and Notes Column */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Starting Row</Label>
              <Input
                type="number"
                min="1"
                value={columnMapping.starting_row}
                onChange={(e) => handleMappingChange('starting_row', parseInt(e.target.value) || 1)}
                className="w-full"
              />
              <p className="text-xs text-gray-600">
                Row number where test case data starts
              </p>
            </div>

            <div className="space-y-2">
              <Label>Notes Column (Optional)</Label>
              <Select
                value={columnMapping.notes_column || 'None'}
                onValueChange={(value) => handleMappingChange('notes_column', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select notes column" />
                </SelectTrigger>
                <SelectContent>
                  {availableColumns.map(column => (
                    <SelectItem key={column} value={column}>
                      {column}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>




    </div>
  );
}
