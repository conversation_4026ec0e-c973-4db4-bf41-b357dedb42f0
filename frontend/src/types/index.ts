export interface Project {
  id: number;
  name: string;
  slug: string;
  description?: string | null;
  code_source?: string | null;
  automation_framework?: string | null;
  programming_language?: string | null;
  project_type?: number;
  created_at: string;
  updated_at?: string;
}

export interface ProjectListItem {
  id: number;
  name: string;
  slug: string;
  description?: string | null;
  created_at: string;
  member_count: number;
  requirement_count: number;
  test_case_count?: number;
  user_role: 'owner' | 'member';
}

// Project Types
export enum ProjectType {
  GENERATE_FROM_REQUIREMENTS = 1,
  IMPORT_TEST_CASES = 2
}

// File Import Types for Project Type 2
export interface FileUploadResponse {
  file_id: string;
  filename: string;
  sheets: string[];
  total_rows: number;
}

export interface SheetPreview {
  sheet_name: string;
  headers: string[];
  sample_rows: string[][];
  total_rows: number;
}

export interface ColumnMapping {
  submodule_column?: string;
  title_column?: string;
  steps_column: string;
  expected_result_column?: string;
  notes_column?: string;
  starting_row: number;
}

export interface ImportRequest {
  file_id: string;
  selected_sheets: string[];
  column_mapping: ColumnMapping;
}

export interface ImportedTestCase {
  title?: string;
  steps: string;
  expected_result?: string;
  notes?: string;
}

export interface ImportedRequirement {
  name: string;
  description: string;
  test_cases: ImportedTestCase[];
}

export interface RequirementChangeSummary {
  requirement_name: string;
  new_test_cases_added: number;
  test_cases_deleted: number;
  test_cases_updated: number;
  is_new_requirement: boolean;
}

export interface ImportResponse {
  requirements: ImportedRequirement[];
  total_test_cases: number;
  message: string;
  change_summary: RequirementChangeSummary[];
}
